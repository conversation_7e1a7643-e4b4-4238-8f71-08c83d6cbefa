---
import Layout from '../layouts/Layout.astro';
import MinimalAcctButton from '../components/MinimalAcctButton.astro';

// SEO
const pageTitle = 'Add Your Business to ExpatsList - Free Listing & Verification';
const pageDescription = 'List your business on ExpatsList for free! Get verified with our $49 lifetime verification to stand out and build trust with expat customers.';
const canonicalUrl = 'https://expatslist.org/add-your-business';
---

<Layout title={pageTitle} description={pageDescription} canonical={canonicalUrl}>
  <!-- Minimal Account Button -->
  <MinimalAcctButton />

  <main class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 pt-16 sm:pt-12">
    <!-- Hero Section -->
    <div class="relative overflow-hidden">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="text-center">
          <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
            <span class="mr-2">✨</span>
            Join thousands of businesses serving expats
          </div>
          
          <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Add Your Business to <span class="text-blue-600">ExpatsList</span>
          </h1>
          
          <p class="text-xl md:text-2xl text-gray-600 mb-8 max-w-4xl mx-auto leading-relaxed">
            Connect with thousands of expats looking for trusted local services. 
            <span class="font-semibold text-blue-600">Start for free</span> and get verified to stand out from the competition.
          </p>

          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <a
              href="/list-business"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold rounded-2xl transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
            >
              <span class="mr-3 text-xl">🚀</span>
              Start Free Listing
            </a>
            <a
              href="#verification-info"
              class="inline-flex items-center px-8 py-4 bg-white hover:bg-gray-50 text-gray-700 font-bold rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl border border-gray-200"
            >
              <span class="mr-3 text-xl">🏆</span>
              Learn About Verification
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">How It Works</h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">Simple steps to get your business discovered by expats</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Step 1 -->
        <div class="text-center">
          <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-3xl text-white">📝</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">1. Submit Your Business</h3>
          <p class="text-gray-600 leading-relaxed">
            Fill out our simple form with your business details. It's completely <strong>free</strong> and takes just a few minutes.
          </p>
        </div>

        <!-- Step 2 -->
        <div class="text-center">
          <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-3xl text-white">✅</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">2. Admin Approval</h3>
          <p class="text-gray-600 leading-relaxed">
            Our team reviews your submission to ensure quality. You'll hear back from us within <strong>24 hours</strong>.
          </p>
        </div>

        <!-- Step 3 -->
        <div class="text-center">
          <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <span class="text-3xl text-white">🌟</span>
          </div>
          <h3 class="text-xl font-bold text-gray-900 mb-4">3. Go Live & Grow</h3>
          <p class="text-gray-600 leading-relaxed">
            Your business goes live and starts attracting expat customers. Optionally get <strong>verified</strong> to build more trust.
          </p>
        </div>
      </div>
    </div>

    <!-- Verification Section -->
    <div id="verification-info" class="bg-gradient-to-br from-amber-50 to-orange-50 py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <div class="inline-flex items-center px-4 py-2 bg-amber-100 text-amber-800 rounded-full text-sm font-medium mb-6">
            <span class="mr-2">🏆</span>
            Premium Verification Available
          </div>
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Get Verified & Stand Out</h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Build trust with expat customers through our verification process
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- Verification Benefits -->
          <div>
            <h3 class="text-2xl font-bold text-gray-900 mb-6">Why Get Verified?</h3>
            <div class="space-y-4">
              <div class="flex items-start">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-xs">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Verified Badge</h4>
                  <p class="text-gray-600">Display a trust badge that shows you're a legitimate business</p>
                </div>
              </div>
              
              <div class="flex items-start">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-xs">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Higher Rankings</h4>
                  <p class="text-gray-600">Verified businesses appear higher in search results</p>
                </div>
              </div>
              
              <div class="flex items-start">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-xs">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Customer Trust</h4>
                  <p class="text-gray-600">Expats prefer verified businesses for peace of mind</p>
                </div>
              </div>
              
              <div class="flex items-start">
                <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span class="text-white text-xs">✓</span>
                </div>
                <div>
                  <h4 class="font-semibold text-gray-900">Lifetime Verification</h4>
                  <p class="text-gray-600">One-time payment, verified status forever</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Verification Pricing -->
          <div class="bg-white rounded-3xl shadow-2xl p-8 border border-amber-200">
            <div class="text-center">
              <div class="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span class="text-2xl text-white">🏆</span>
              </div>
              <h3 class="text-2xl font-bold text-gray-900 mb-2">Business Verification</h3>
              <div class="text-4xl font-bold text-amber-600 mb-2">$49 USD</div>
              <p class="text-gray-600 mb-6">One-time lifetime fee</p>
              
              <div class="bg-amber-50 rounded-xl p-4 mb-6">
                <h4 class="font-semibold text-gray-900 mb-2">Required Documents:</h4>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• Business registration certificate</li>
                  <li>• Tax identification documents</li>
                  <li>• Proof of business address</li>
                  <li>• Valid business license (if applicable)</li>
                </ul>
              </div>
              
              <p class="text-sm text-gray-500 mb-6">
                Verification process takes 2-3 business days after document submission
              </p>
              
              <button
                onclick="handleVerificationPayment()"
                class="w-full px-6 py-3 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Pay for Verification - $49 USD
              </button>
              <p class="text-xs text-gray-500 mt-2 text-center">
                Secure payment via PayPal • One-time lifetime fee
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Free vs Verified Comparison -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Free vs Verified Listing</h2>
        <p class="text-xl text-gray-600">Choose what works best for your business</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Free Listing -->
        <div class="bg-white rounded-2xl shadow-lg p-8 border border-gray-200">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-2xl text-white">🆓</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900">Free Listing</h3>
            <div class="text-3xl font-bold text-blue-600 mt-2">$0</div>
          </div>
          
          <ul class="space-y-3 mb-8">
            <li class="flex items-center">
              <span class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700">Basic business listing</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700">Contact information display</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700">Business description</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700">Location & hours</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✗</span>
              </span>
              <span class="text-gray-400">Verification badge</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✗</span>
              </span>
              <span class="text-gray-400">Priority in search results</span>
            </li>
          </ul>
          
          <a
            href="/list-business"
            class="w-full inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            Start Free Listing
          </a>
        </div>

        <!-- Verified Listing -->
        <div class="bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl shadow-lg p-8 border-2 border-amber-300 relative">
          <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <span class="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold">
              RECOMMENDED
            </span>
          </div>
          
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <span class="text-2xl text-white">🏆</span>
            </div>
            <h3 class="text-2xl font-bold text-gray-900">Verified Listing</h3>
            <div class="text-3xl font-bold text-amber-600 mt-2">$49</div>
            <p class="text-sm text-gray-600">One-time lifetime fee</p>
          </div>
          
          <ul class="space-y-3 mb-8">
            <li class="flex items-center">
              <span class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700">Everything in Free Listing</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700 font-semibold">Verified business badge</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700 font-semibold">Higher search rankings</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700 font-semibold">Enhanced customer trust</span>
            </li>
            <li class="flex items-center">
              <span class="w-5 h-5 bg-amber-500 rounded-full flex items-center justify-center mr-3">
                <span class="text-white text-xs">✓</span>
              </span>
              <span class="text-gray-700 font-semibold">Priority customer support</span>
            </li>
          </ul>
          
          <button
            onclick="handleVerificationPayment()"
            class="w-full inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            Pay for Verification - $49 USD
          </button>
          <p class="text-xs text-gray-500 mt-2 text-center">
            Secure payment via PayPal • One-time lifetime fee
          </p>
        </div>
      </div>
    </div>

    <!-- Final CTA -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-700 py-16">
      <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
          Ready to Connect with Expat Customers?
        </h2>
        <p class="text-xl text-blue-100 mb-8">
          Join thousands of businesses already serving the expat community
        </p>
        <a
          href="/list-business"
          class="inline-flex items-center px-8 py-4 bg-white hover:bg-gray-100 text-blue-600 font-bold rounded-2xl transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
        >
          <span class="mr-3 text-xl">🚀</span>
          Start Your Free Listing Now
        </a>
      </div>
    </div>
  </main>
</Layout>

<script>
  // Handle verification payment
  function handleVerificationPayment() {
    // Check if user is authenticated
    if (!window.authFunctions) {
      alert('Please wait for the page to load completely and try again.');
      return;
    }

    const currentUser = window.authFunctions.getCurrentUser();

    if (!currentUser) {
      alert('Please sign in first to proceed with verification payment.');
      window.authFunctions.showAuthModal();
      return;
    }

    // Show confirmation dialog
    const confirmed = confirm(
      'You will be redirected to PayPal to pay $49 USD for business verification.\n\n' +
      'This is a one-time lifetime fee. After payment, your verification request will be sent to our admin team for approval.\n\n' +
      'Continue to PayPal?'
    );

    if (confirmed) {
      // Create PayPal payment URL
      const paypalUrl = 'https://www.paypal.com/cgi-bin/webscr?' +
        'cmd=_xclick&' +
        'business=<EMAIL>&' +
        'item_name=ExpatsList Business Verification&' +
        'amount=49.00&' +
        'currency_code=USD&' +
        'return=' + encodeURIComponent(window.location.origin + '/verification-success') + '&' +
        'cancel_return=' + encodeURIComponent(window.location.origin + '/add-your-business') + '&' +
        'custom=' + encodeURIComponent(currentUser.id) + '&' +
        'notify_url=' + encodeURIComponent(window.location.origin + '/api/paypal-ipn');

      // Redirect to PayPal
      window.location.href = paypalUrl;
    }
  }

  // Make function globally available
  window.handleVerificationPayment = handleVerificationPayment;
</script>
