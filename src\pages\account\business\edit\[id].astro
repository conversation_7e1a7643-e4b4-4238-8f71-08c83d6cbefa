---
import Layout from '../../../../layouts/Layout.astro';
import Navigation from '../../../../components/Navigation.astro';
import { query } from '../../../../lib/database';

export const prerender = false;

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/account');
}

// Get business listing details
const listingResult = await query(`
  SELECT
    l.*,
    c.name as category_name,
    c.slug as category_slug,
    ci.name as city_name,
    ci.path_slug as city_slug
  FROM listings l
  LEFT JOIN categories c ON l.category_primary_id = c.id
  LEFT JOIN cities ci ON l.city_id = ci.id
  WHERE l.id = $1 AND l.listing_status = 'active'
`, [id]);

const listing = listingResult.rows[0];

if (!listing) {
  return Astro.redirect('/account?error=business_not_found');
}

// Get cities and categories for dropdowns
const citiesResult = await query('SELECT * FROM cities ORDER BY name');
const cities = citiesResult.rows;

const categoriesResult = await query('SELECT * FROM categories ORDER BY sort_order, name');
const categories = categoriesResult.rows;

// Parse contact info and images
let contactInfo: any = {};
let imageGallery: string[] = [];

try {
  if (listing.contact_info) {
    contactInfo = typeof listing.contact_info === 'string'
      ? JSON.parse(listing.contact_info)
      : listing.contact_info;
  }
} catch (e) {
  console.error('Error parsing contact info:', e);
}

try {
  if (listing.image_gallery_paths) {
    imageGallery = Array.isArray(listing.image_gallery_paths)
      ? listing.image_gallery_paths
      : JSON.parse(listing.image_gallery_paths);
  }
} catch (e) {
  console.error('Error parsing image gallery:', e);
}

const pageTitle = `Edit ${listing.business_name} - ExpatsList`;
const pageDescription = `Edit your business listing for ${listing.business_name} on ExpatsList.`;
---

<Layout title={pageTitle} description={pageDescription}>
  <Navigation currentPath="/account" />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200 py-6">
      <div class="max-w-4xl mx-auto px-4 sm:px-6">
        <div class="flex items-center justify-between">
          <div>
            <a href="/account" class="text-blue-600 hover:text-blue-800 text-sm font-medium mb-2 inline-block">
              ← Back to Account
            </a>
            <h1 class="text-2xl font-bold text-slate-900">Edit Business Listing</h1>
            <p class="text-slate-600 text-sm mt-1">Update your business information and photos</p>
          </div>
          <div class="flex items-center space-x-2">
            <span class="px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
              ✅ Active & Claimed
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Form Content -->
    <div class="py-8">
      <div class="max-w-4xl mx-auto px-4 sm:px-6">
        <form id="business-edit-form" class="space-y-8">
          <input type="hidden" id="business_id" value={listing.id} />

          <!-- Basic Information -->
          <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <h2 class="text-lg font-semibold text-slate-900 mb-6">Basic Information</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="business_name" class="block text-sm font-medium text-slate-700 mb-2">
                  Business Name *
                </label>
                <input
                  type="text"
                  id="business_name"
                  name="business_name"
                  required
                  value={listing.business_name}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Your business name"
                />
              </div>

              <div>
                <label for="display_name" class="block text-sm font-medium text-slate-700 mb-2">
                  Display Name
                </label>
                <input
                  type="text"
                  id="display_name"
                  name="display_name"
                  value={listing.display_name || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Optional display name"
                />
                <p class="text-xs text-slate-500 mt-1">Leave blank to use business name</p>
              </div>

              <div>
                <label for="city_id" class="block text-sm font-medium text-slate-700 mb-2">
                  Location *
                </label>
                <select
                  id="city_id"
                  name="city_id"
                  required
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {cities.map(city => (
                    <option value={city.id} selected={city.id === listing.city_id}>
                      {city.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label for="category_primary_id" class="block text-sm font-medium text-slate-700 mb-2">
                  Category *
                </label>
                <select
                  id="category_primary_id"
                  name="category_primary_id"
                  required
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {categories.map(category => (
                    <option value={category.id} selected={category.id === listing.category_primary_id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div class="md:col-span-2">
                <label for="address_full" class="block text-sm font-medium text-slate-700 mb-2">
                  Full Address
                </label>
                <input
                  type="text"
                  id="address_full"
                  name="address_full"
                  value={listing.address_full || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Street address, city, state/province"
                />
              </div>
            </div>
          </div>

          <!-- Descriptions -->
          <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <h2 class="text-lg font-semibold text-slate-900 mb-6">Descriptions</h2>

            <div class="space-y-6">
              <div>
                <label for="description_short" class="block text-sm font-medium text-slate-700 mb-2">
                  Short Description *
                </label>
                <textarea
                  id="description_short"
                  name="description_short"
                  required
                  rows="3"
                  maxlength="200"
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="Brief description of your business (max 200 characters)"
                >{listing.description_short}</textarea>
                <div class="flex justify-between items-center mt-1">
                  <p class="text-xs text-slate-500">This appears in search results and previews</p>
                  <span id="short-desc-count" class="text-xs text-slate-500">0/200</span>
                </div>
              </div>

              <div>
                <label for="description_long" class="block text-sm font-medium text-slate-700 mb-2">
                  Detailed Description
                </label>
                <textarea
                  id="description_long"
                  name="description_long"
                  rows="6"
                  maxlength="1000"
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="Detailed description of your business, services, and what makes you special"
                >{listing.description_long || ''}</textarea>
                <div class="flex justify-between items-center mt-1">
                  <p class="text-xs text-slate-500">Detailed information for your business page</p>
                  <span id="long-desc-count" class="text-xs text-slate-500">0/1000</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Business Photos -->
          <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <h2 class="text-lg font-semibold text-slate-900 mb-6">Business Photos</h2>
            <p class="text-sm text-slate-600 mb-6">Upload up to 2 high-quality photos of your business</p>

            <!-- Current Photos -->
            <div id="current-photos" class="mb-6">
              <h3 class="text-sm font-medium text-slate-700 mb-3">Current Photos</h3>
              <div id="current-photos-grid" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Current photos will be populated by JavaScript -->
              </div>
            </div>

            <!-- Photo Upload Area -->
            <div class="space-y-4">
              <div class="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors bg-slate-50 hover:bg-blue-50">
                <input
                  type="file"
                  id="business_image_upload"
                  accept="image/jpeg,image/jpg,image/png,image/webp"
                  multiple
                  class="hidden"
                />
                <label for="business_image_upload" class="cursor-pointer">
                  <div class="space-y-3">
                    <div class="text-4xl text-slate-400">📷</div>
                    <div class="text-lg font-medium text-slate-700">Click to upload new photos</div>
                    <div class="text-sm text-slate-500">JPEG, PNG, or WebP (max 3MB each)</div>
                    <div class="text-xs text-slate-400">Maximum 2 photos total</div>
                  </div>
                </label>
              </div>

              <!-- Upload Status -->
              <div id="upload_status" class="hidden">
                <div class="flex items-center space-x-2 text-sm">
                  <div class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  <span class="text-blue-600">Uploading images...</span>
                </div>
              </div>

              <!-- New Image Previews -->
              <div id="new_image_previews" class="grid grid-cols-1 md:grid-cols-2 gap-4 hidden">
                <!-- Preview images will be inserted here -->
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <h2 class="text-lg font-semibold text-slate-900 mb-6">Contact Information</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label for="contact_phone" class="block text-sm font-medium text-slate-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  id="contact_phone"
                  name="contact_phone"
                  value={contactInfo.phone || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+52 ************"
                />
              </div>

              <div>
                <label for="contact_email" class="block text-sm font-medium text-slate-700 mb-2">
                  Business Email
                </label>
                <input
                  type="email"
                  id="contact_email"
                  name="contact_email"
                  value={contactInfo.email || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label for="contact_website" class="block text-sm font-medium text-slate-700 mb-2">
                  Website
                </label>
                <input
                  type="url"
                  id="contact_website"
                  name="contact_website"
                  value={contactInfo.website || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://www.yourbusiness.com"
                />
              </div>

              <div>
                <label for="contact_whatsapp" class="block text-sm font-medium text-slate-700 mb-2">
                  WhatsApp
                </label>
                <input
                  type="tel"
                  id="contact_whatsapp"
                  name="contact_whatsapp"
                  value={contactInfo.whatsapp || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+52 ************"
                />
              </div>

              <div>
                <label for="contact_facebook" class="block text-sm font-medium text-slate-700 mb-2">
                  Facebook Page
                </label>
                <input
                  type="url"
                  id="contact_facebook"
                  name="contact_facebook"
                  value={contactInfo.facebook || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://facebook.com/yourbusiness"
                />
              </div>

              <div>
                <label for="contact_instagram" class="block text-sm font-medium text-slate-700 mb-2">
                  Instagram
                </label>
                <input
                  type="url"
                  id="contact_instagram"
                  name="contact_instagram"
                  value={contactInfo.instagram || ''}
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="https://instagram.com/yourbusiness"
                />
              </div>
            </div>
          </div>

          <!-- Additional Details -->
          <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <h2 class="text-lg font-semibold text-slate-900 mb-6">Additional Details</h2>

            <div class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label for="languages_spoken" class="block text-sm font-medium text-slate-700 mb-2">
                    Languages Spoken
                  </label>
                  <input
                    type="text"
                    id="languages_spoken"
                    name="languages_spoken"
                    value={Array.isArray(listing.languages_spoken) ? listing.languages_spoken.join(', ') : listing.languages_spoken || ''}
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="English, Spanish, French"
                  />
                  <p class="text-xs text-slate-500 mt-1">Separate multiple languages with commas</p>
                </div>

                <div>
                  <label for="price_range" class="block text-sm font-medium text-slate-700 mb-2">
                    Price Range
                  </label>
                  <select
                    id="price_range"
                    name="price_range"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select price range</option>
                    <option value="budget" selected={listing.price_range === 'budget'}>$ Budget-friendly</option>
                    <option value="moderate" selected={listing.price_range === 'moderate'}>$$ Moderate</option>
                    <option value="upscale" selected={listing.price_range === 'upscale'}>$$$ Upscale</option>
                    <option value="luxury" selected={listing.price_range === 'luxury'}>$$$$ Luxury</option>
                  </select>
                </div>
              </div>

              <div>
                <label for="services_offered" class="block text-sm font-medium text-slate-700 mb-2">
                  Services Offered
                </label>
                <textarea
                  id="services_offered"
                  name="services_offered"
                  rows="3"
                  class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                  placeholder="List your main services or specialties"
                >{Array.isArray(listing.services_offered_keywords) ? listing.services_offered_keywords.join(', ') : listing.services_offered_keywords || ''}</textarea>
                <p class="text-xs text-slate-500 mt-1">Describe your main services, separated by commas</p>
              </div>

              <!-- Business Features -->
              <div>
                <h3 class="text-sm font-medium text-slate-700 mb-3">Business Features</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <label class="flex items-center space-x-3 p-3 border border-slate-200 rounded-lg hover:bg-slate-50 cursor-pointer">
                    <input
                      type="checkbox"
                      name="owner_is_expat"
                      checked={listing.owner_is_expat}
                      class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                    />
                    <span class="text-sm text-slate-700">Owner is Expat</span>
                  </label>

                  <label class="flex items-center space-x-3 p-3 border border-slate-200 rounded-lg hover:bg-slate-50 cursor-pointer">
                    <input
                      type="checkbox"
                      name="pet_friendly"
                      checked={listing.pet_friendly}
                      class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                    />
                    <span class="text-sm text-slate-700">Pet Friendly</span>
                  </label>

                  <label class="flex items-center space-x-3 p-3 border border-slate-200 rounded-lg hover:bg-slate-50 cursor-pointer">
                    <input
                      type="checkbox"
                      name="kid_friendly"
                      checked={listing.kid_friendly}
                      class="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                    />
                    <span class="text-sm text-slate-700">Kid Friendly</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
            <div class="flex items-center justify-between">
              <div>
                <h3 class="text-sm font-medium text-slate-700">Ready to update your business?</h3>
                <p class="text-xs text-slate-500 mt-1">Changes will be visible immediately on your listing</p>
              </div>
              <div class="flex space-x-3">
                <button
                  type="button"
                  onclick="window.location.href='/account'"
                  class="px-6 py-3 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  id="submit-btn"
                  class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center"
                >
                  <span id="submit-text">Update Business</span>
                  <div id="submit-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </main>

  <script is:inline define:vars={{ listing, contactInfo, imageGallery }}>
    // Global variables
    let currentUser = null;
    let uploadedImages = [];
    let currentImages = imageGallery || [];
    let isSubmitting = false;

    // Initialize page
    document.addEventListener('DOMContentLoaded', async () => {
      await checkAuthentication();
      initializeForm();
      loadCurrentPhotos();
      setupEventListeners();
    });

    // Check if user is authenticated and owns this business
    async function checkAuthentication() {
      if (!window.authFunctions) {
        setTimeout(checkAuthentication, 100);
        return;
      }

      currentUser = window.authFunctions.getCurrentUser();
      if (!currentUser) {
        window.location.href = '/account';
        return;
      }

      // Verify ownership
      if (listing.owner_user_id !== currentUser.id && listing.claimed_by_user_id !== currentUser.id) {
        alert('You do not have permission to edit this business.');
        window.location.href = '/account';
        return;
      }
    }

    // Initialize form with current data
    function initializeForm() {
      // Update character counters
      updateCharacterCount('description_short', 'short-desc-count', 200);
      updateCharacterCount('description_long', 'long-desc-count', 1000);
    }

    // Load current photos
    function loadCurrentPhotos() {
      const container = document.getElementById('current-photos-grid');
      if (!container) return;

      if (currentImages.length === 0) {
        container.innerHTML = `
          <div class="col-span-2 text-center py-8 text-slate-500">
            <span class="text-4xl mb-2 block">📷</span>
            <p>No photos uploaded yet</p>
          </div>
        `;
        return;
      }

      container.innerHTML = currentImages.map((imageUrl, index) => `
        <div class="relative group">
          <img src="${imageUrl}" alt="Business photo ${index + 1}" class="w-full h-48 object-cover rounded-lg border border-slate-200" />
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-lg flex items-center justify-center">
            <button
              onclick="removeCurrentPhoto('${imageUrl}', ${index})"
              class="opacity-0 group-hover:opacity-100 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all"
            >
              Remove
            </button>
          </div>
          ${imageUrl === listing.main_image_path ? `
            <div class="absolute top-2 left-2 bg-blue-600 text-white px-2 py-1 rounded text-xs font-medium">
              Main Photo
            </div>
          ` : `
            <button
              onclick="setMainPhoto('${imageUrl}')"
              class="absolute top-2 right-2 bg-white bg-opacity-90 hover:bg-opacity-100 text-slate-700 px-2 py-1 rounded text-xs font-medium transition-all opacity-0 group-hover:opacity-100"
            >
              Set as Main
            </button>
          `}
        </div>
      `).join('');
    }

    // Setup event listeners
    function setupEventListeners() {
      // Character counters
      document.getElementById('description_short')?.addEventListener('input', (e) => {
        updateCharacterCount('description_short', 'short-desc-count', 200);
      });

      document.getElementById('description_long')?.addEventListener('input', (e) => {
        updateCharacterCount('description_long', 'long-desc-count', 1000);
      });

      // Image upload
      document.getElementById('business_image_upload')?.addEventListener('change', handleImageUpload);

      // Form submission
      document.getElementById('business-edit-form')?.addEventListener('submit', handleFormSubmit);
    }

    // Update character count
    function updateCharacterCount(fieldId, counterId, maxLength) {
      const field = document.getElementById(fieldId);
      const counter = document.getElementById(counterId);
      if (field && counter) {
        const length = field.value.length;
        counter.textContent = `${length}/${maxLength}`;
        counter.className = length > maxLength ? 'text-xs text-red-500' : 'text-xs text-slate-500';
      }
    }

    // Handle image upload
    async function handleImageUpload(event) {
      const files = Array.from(event.target.files);
      if (files.length === 0) return;

      // Check total photo limit (current + new)
      const totalPhotos = currentImages.length + uploadedImages.length + files.length;
      if (totalPhotos > 2) {
        alert('You can only have a maximum of 2 photos. Please remove some existing photos first.');
        event.target.value = '';
        return;
      }

      // Validate files
      for (const file of files) {
        if (file.size > 3 * 1024 * 1024) {
          alert(`${file.name} is too large. Please choose images under 3MB.`);
          event.target.value = '';
          return;
        }

        if (!file.type.match(/^image\/(jpeg|jpg|png|webp)$/)) {
          alert(`${file.name} is not a supported image format. Please use JPEG, PNG, or WebP.`);
          event.target.value = '';
          return;
        }
      }

      // Show upload status
      const statusDiv = document.getElementById('upload_status');
      statusDiv?.classList.remove('hidden');

      try {
        for (const file of files) {
          const formData = new FormData();
          formData.append('image', file);
          formData.append('userId', currentUser.id);

          const response = await fetch('/api/business/upload-image', {
            method: 'POST',
            body: formData
          });

          const result = await response.json();

          if (result.success) {
            uploadedImages.push({
              url: result.imageUrl,
              file: file
            });
          } else {
            throw new Error(result.error || 'Upload failed');
          }
        }

        updateNewImagePreviews();
        showSuccess('Images uploaded successfully!');
      } catch (error) {
        console.error('Upload error:', error);
        showError('Failed to upload images: ' + error.message);
      } finally {
        statusDiv?.classList.add('hidden');
        event.target.value = '';
      }
    }

    // Update new image previews
    function updateNewImagePreviews() {
      const container = document.getElementById('new_image_previews');
      if (!container) return;

      if (uploadedImages.length === 0) {
        container.classList.add('hidden');
        return;
      }

      container.classList.remove('hidden');
      container.innerHTML = uploadedImages.map((img, index) => `
        <div class="relative group">
          <img src="${img.url}" alt="New photo ${index + 1}" class="w-full h-48 object-cover rounded-lg border border-slate-200" />
          <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 rounded-lg flex items-center justify-center">
            <button
              onclick="removeNewPhoto(${index})"
              class="opacity-0 group-hover:opacity-100 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all"
            >
              Remove
            </button>
          </div>
          <div class="absolute top-2 left-2 bg-green-600 text-white px-2 py-1 rounded text-xs font-medium">
            New Photo
          </div>
        </div>
      `).join('');
    }

    // Remove current photo
    window.removeCurrentPhoto = function(imageUrl, index) {
      if (confirm('Are you sure you want to remove this photo?')) {
        currentImages.splice(index, 1);
        loadCurrentPhotos();
        showSuccess('Photo will be removed when you save changes');
      }
    };

    // Remove new photo
    window.removeNewPhoto = function(index) {
      uploadedImages.splice(index, 1);
      updateNewImagePreviews();
    };

    // Set main photo
    window.setMainPhoto = function(imageUrl) {
      // This will be handled in form submission
      showSuccess('Main photo will be updated when you save changes');
    };

    // Handle form submission
    async function handleFormSubmit(event) {
      event.preventDefault();

      if (isSubmitting) return;
      isSubmitting = true;

      const submitBtn = document.getElementById('submit-btn');
      const submitText = document.getElementById('submit-text');
      const submitSpinner = document.getElementById('submit-spinner');

      submitBtn.disabled = true;
      submitText.textContent = 'Updating...';
      submitSpinner?.classList.remove('hidden');

      try {
        const formData = new FormData(event.target);
        const data = {};

        // Convert form data to object
        for (const [key, value] of formData.entries()) {
          if (key === 'languages_spoken' || key === 'services_offered') {
            data[key] = value.toString().split(',').map(s => s.trim()).filter(s => s);
          } else if (key === 'owner_is_expat' || key === 'pet_friendly' || key === 'kid_friendly') {
            data[key] = formData.has(key);
          } else {
            data[key] = value;
          }
        }

        // Add contact info
        data.contact_info = {
          phone: formData.get('contact_phone') || '',
          email: formData.get('contact_email') || '',
          website: formData.get('contact_website') || '',
          whatsapp: formData.get('contact_whatsapp') || '',
          facebook: formData.get('contact_facebook') || '',
          instagram: formData.get('contact_instagram') || ''
        };

        // Add image data
        data.current_images = currentImages;
        data.new_images = uploadedImages.map(img => img.url);

        // Set main image (first current image or first new image)
        if (currentImages.length > 0) {
          data.main_image_url = currentImages[0];
        } else if (uploadedImages.length > 0) {
          data.main_image_url = uploadedImages[0].url;
        }

        // Add business ID
        data.business_id = document.getElementById('business_id').value;

        const response = await fetch('/api/business/update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
          showSuccess('Business updated successfully!');
          setTimeout(() => {
            window.location.href = '/account';
          }, 2000);
        } else {
          throw new Error(result.error || 'Update failed');
        }
      } catch (error) {
        console.error('Update error:', error);
        showError('Failed to update business: ' + error.message);
      } finally {
        isSubmitting = false;
        submitBtn.disabled = false;
        submitText.textContent = 'Update Business';
        submitSpinner?.classList.add('hidden');
      }
    }

    // Utility functions
    function showSuccess(message) {
      const alertDiv = document.createElement('div');
      alertDiv.className = 'fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-4 shadow-lg z-50 max-w-sm';
      alertDiv.innerHTML = `
        <div class="flex items-center space-x-3">
          <span class="text-green-500 text-lg">✅</span>
          <div class="flex-1">
            <p class="font-medium text-green-900">Success</p>
            <p class="text-green-800 text-sm">${message}</p>
          </div>
        </div>
      `;
      document.body.appendChild(alertDiv);
      setTimeout(() => alertDiv.remove(), 5000);
    }

    function showError(message) {
      const alertDiv = document.createElement('div');
      alertDiv.className = 'fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg z-50 max-w-sm';
      alertDiv.innerHTML = `
        <div class="flex items-center space-x-3">
          <span class="text-red-500 text-lg">⚠️</span>
          <div class="flex-1">
            <p class="font-medium text-red-900">Error</p>
            <p class="text-red-800 text-sm">${message}</p>
          </div>
        </div>
      `;
      document.body.appendChild(alertDiv);
      setTimeout(() => alertDiv.remove(), 8000);
    }
  </script>
</Layout>