import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { userId, userEmail, type, subject, message, relatedListingId } = await request.json();

    if (!userId || !userEmail || !type || !subject || !message) {
      return new Response(JSON.stringify({ 
        error: 'User ID, email, type, subject, and message are required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Map appeal types to valid database types
    let dbType = type;
    if (type === 'business_rejection_appeal') {
      dbType = 'business_correction';
    }

    // Validate support ticket type
    const validTypes = ['bug_report', 'feature_request', 'business_correction', 'account_help', 'general_inquiry', 'other'];
    if (!validTypes.includes(dbType)) {
      return new Response(JSON.stringify({
        error: 'Invalid support ticket type'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Insert support request into database
    const result = await query(`
      INSERT INTO support_requests (
        user_id, user_email, type, subject, message, status, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
      RETURNING *
    `, [userId, userEmail, dbType, subject, message, 'open']);

    // If this is related to a business listing, create an admin notification
    if (relatedListingId && type === 'business_rejection_appeal') {
      try {
        // Get business details
        const businessResult = await query(`
          SELECT business_name FROM listings WHERE id = $1
        `, [relatedListingId]);

        const businessName = businessResult.rows[0]?.business_name || 'Unknown Business';

        await query(`
          INSERT INTO admin_notifications (
            type,
            title,
            message,
            data,
            created_at
          ) VALUES ($1, $2, $3, $4, NOW())
        `, [
          'business_rejection_appeal',
          'Business Rejection Appeal',
          `User ${userEmail} has appealed the rejection of "${businessName}"`,
          JSON.stringify({
            supportRequestId: result.rows[0].id,
            listingId: relatedListingId,
            businessName,
            userEmail,
            userId
          })
        ]);
      } catch (notificationError) {
        console.log('Admin notification failed:', notificationError);
      }
    }

    return new Response(JSON.stringify({
      success: true,
      request: result.rows[0]
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in submit support ticket API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
