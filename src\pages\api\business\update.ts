import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();

    // Validate required fields
    if (!data.business_id || !data.business_name) {
      return new Response(JSON.stringify({
        error: 'Business ID and business name are required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get current user from auth (this would normally be from session/token)
    // For now, we'll verify ownership through the database
    const ownershipCheck = await query(`
      SELECT owner_user_id, claimed_by_user_id 
      FROM listings 
      WHERE id = $1 AND listing_status = 'active'
    `, [data.business_id]);

    if (ownershipCheck.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Business not found or not active'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Prepare contact info
    const contactInfo = data.contact_info || {};
    
    // Prepare image gallery
    let imageGallery = [];
    let mainImagePath = null;

    // Combine current and new images
    if (data.current_images && Array.isArray(data.current_images)) {
      imageGallery = [...data.current_images];
    }
    
    if (data.new_images && Array.isArray(data.new_images)) {
      imageGallery = [...imageGallery, ...data.new_images];
    }

    // Limit to 2 photos
    if (imageGallery.length > 2) {
      imageGallery = imageGallery.slice(0, 2);
    }

    // Set main image
    if (data.main_image_url) {
      mainImagePath = data.main_image_url;
    } else if (imageGallery.length > 0) {
      mainImagePath = imageGallery[0];
    }

    // Update the business listing
    const updateResult = await query(`
      UPDATE listings SET
        business_name = $2,
        display_name = $3,
        city_id = $4,
        category_primary_id = $5,
        description_short = $6,
        description_long = $7,
        contact_info = $8,
        address_full = $9,
        languages_spoken = $10,
        services_offered_keywords = $11,
        price_range = $12,
        owner_is_expat = $13,
        pet_friendly = $14,
        kid_friendly = $15,
        main_image_path = $16,
        image_gallery_paths = $17,
        updated_at = NOW()
      WHERE id = $1
      RETURNING *
    `, [
      data.business_id,
      data.business_name,
      data.display_name || null,
      data.city_id,
      data.category_primary_id,
      data.description_short,
      data.description_long || null,
      Object.keys(contactInfo).length > 0 ? JSON.stringify(contactInfo) : null,
      data.address_full || null,
      data.languages_spoken || null,
      data.services_offered || null,
      data.price_range || null,
      data.owner_is_expat || false,
      data.pet_friendly || false,
      data.kid_friendly || false,
      mainImagePath,
      imageGallery.length > 0 ? JSON.stringify(imageGallery) : null
    ]);

    if (updateResult.rows.length === 0) {
      return new Response(JSON.stringify({
        error: 'Failed to update business listing'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      business: updateResult.rows[0]
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in business update API:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
