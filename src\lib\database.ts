import { Pool } from 'pg';
import { cache, cacheKeys, cacheTTL, getCachedData } from './cache';

// Database connection pool for better performance
const DATABASE_URL = import.meta.env.DATABASE_URL ||
  `postgresql://postgres.ltpeowkkfassadoerorm:<EMAIL>:6543/postgres`;

// Create connection pool
const pool = new Pool({
  connectionString: DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  },
  // Pool configuration for optimal performance
  max: 20,                    // Maximum number of clients in the pool
  min: 2,                     // Minimum number of clients in the pool
  idleTimeoutMillis: 30000,   // Close idle clients after 30 seconds
  connectionTimeoutMillis: 5000, // Timeout for new connections
  query_timeout: 10000,       // Query timeout
  statement_timeout: 10000,   // Statement timeout
});

// Optimized query function using connection pool
export async function query(text: string, params?: any[]) {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } finally {
    client.release();
  }
}

// Get all cities (cached)
export async function getCities() {
  try {
    const result = await getCachedData(
      cacheKeys.cities(),
      async () => {
        const queryResult = await query('SELECT * FROM cities ORDER BY name');
        return { data: queryResult.rows, error: null };
      },
      cacheTTL.cities
    );
    return result;
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get city by path slug (cached)
export async function getCityBySlug(slug: string) {
  try {
    const result = await getCachedData(
      cacheKeys.cityBySlug(slug),
      async () => {
        const queryResult = await query('SELECT * FROM cities WHERE path_slug = $1', [slug]);
        return { data: queryResult.rows[0] || null, error: null };
      },
      cacheTTL.cities
    );
    return result;
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get listings for a city (cached)
export async function getListingsForCity(cityId: string) {
  try {
    const result = await getCachedData(
      cacheKeys.listingsForCity(cityId),
      async () => {
        const queryResult = await query(`
          SELECT l.*, c.name as category_name, c.slug as category_slug
          FROM listings l
          LEFT JOIN categories c ON l.category_primary_id = c.id
          WHERE l.city_id = $1 AND l.listing_status = 'active' AND l.deleted_at IS NULL
          ORDER BY l.is_pinned DESC, l.created_at DESC
        `, [cityId]);
        return { data: queryResult.rows, error: null };
      },
      cacheTTL.listings
    );
    return result;
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get categories (cached)
export async function getCategories() {
  try {
    const result = await getCachedData(
      cacheKeys.categories(),
      async () => {
        const queryResult = await query('SELECT * FROM categories ORDER BY sort_order, name');
        return { data: queryResult.rows, error: null };
      },
      cacheTTL.categories
    );
    return result;
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get listings for a specific subcategory with weighted randomization (cached)
export async function getListingsForSubcategory(cityId: string, subcategoryId: string) {
  try {
    const result = await getCachedData(
      cacheKeys.listingsForSubcategory(cityId, subcategoryId),
      async () => {
        const queryResult = await query(`
          SELECT l.*, c.name as category_name, c.slug as category_slug,
          -- Calculate weighted score for intelligent ordering
          (
            CASE WHEN l.is_pinned = true THEN 1000 ELSE 0 END +
            CASE WHEN l.is_verified_expatslist = true THEN 100 ELSE 0 END +
            CASE WHEN l.description_short IS NOT NULL THEN 20 ELSE 0 END +
            CASE WHEN l.description_long IS NOT NULL THEN 15 ELSE 0 END +
            CASE WHEN l.contact_info->>'phone' IS NOT NULL AND l.contact_info->>'phone' != '' THEN 10 ELSE 0 END +
            CASE WHEN l.contact_info->>'email' IS NOT NULL AND l.contact_info->>'email' != '' THEN 10 ELSE 0 END +
            CASE WHEN l.contact_info->>'website' IS NOT NULL AND l.contact_info->>'website' != '' THEN 10 ELSE 0 END +
            CASE WHEN l.view_count > 0 THEN LEAST(l.view_count, 50) ELSE 0 END +
            CASE WHEN l.updated_at > NOW() - INTERVAL '30 days' THEN 25 ELSE 0 END +
            -- Add randomization factor
            (RANDOM() * 50)::INTEGER
          ) as weighted_score
          FROM listings l
          LEFT JOIN categories c ON l.category_primary_id = c.id
          WHERE l.city_id = $1
          AND l.category_primary_id = $2
          AND l.listing_status = 'active'
          AND l.deleted_at IS NULL
          ORDER BY weighted_score DESC, l.created_at DESC
        `, [cityId, subcategoryId]);
        return { data: queryResult.rows, error: null };
      },
      cacheTTL.listings
    );
    return result;
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get listings for a main category (subcategories under it) with weighted randomization (cached)
export async function getListingsForCategory(cityId: string, categoryId: string) {
  try {
    const result = await getCachedData(
      cacheKeys.listingsForCategory(cityId, categoryId),
      async () => {
        const queryResult = await query(`
          SELECT l.*,
                 subcategory.name as category_name,
                 subcategory.slug as category_slug,
                 main_category.name as main_category_name,
                 main_category.slug as main_category_slug,
          -- Calculate weighted score for intelligent ordering
          (
            CASE WHEN l.is_pinned = true THEN 1000 ELSE 0 END +
            CASE WHEN l.is_verified_expatslist = true THEN 100 ELSE 0 END +
            CASE WHEN l.description_short IS NOT NULL THEN 20 ELSE 0 END +
            CASE WHEN l.description_long IS NOT NULL THEN 15 ELSE 0 END +
            CASE WHEN l.contact_info->>'phone' IS NOT NULL AND l.contact_info->>'phone' != '' THEN 10 ELSE 0 END +
            CASE WHEN l.contact_info->>'email' IS NOT NULL AND l.contact_info->>'email' != '' THEN 10 ELSE 0 END +
            CASE WHEN l.contact_info->>'website' IS NOT NULL AND l.contact_info->>'website' != '' THEN 10 ELSE 0 END +
            CASE WHEN l.view_count > 0 THEN LEAST(l.view_count, 50) ELSE 0 END +
            CASE WHEN l.updated_at > NOW() - INTERVAL '30 days' THEN 25 ELSE 0 END +
            -- Add randomization factor
            (RANDOM() * 50)::INTEGER
          ) as weighted_score
          FROM listings l
          LEFT JOIN categories subcategory ON l.category_primary_id = subcategory.id
          LEFT JOIN categories main_category ON subcategory.parent_id = main_category.id
          WHERE l.city_id = $1
          AND subcategory.parent_id = $2
          AND l.listing_status = 'active'
          AND l.deleted_at IS NULL
          ORDER BY weighted_score DESC, l.created_at DESC
        `, [cityId, categoryId]);
        return { data: queryResult.rows, error: null };
      },
      cacheTTL.listings
    );
    return result;
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Get a single business listing by slug and subcategory (cached)
export async function getBusinessListing(cityId: string, subcategoryId: string, businessSlug: string) {
  try {
    const result = await getCachedData(
      cacheKeys.businessListing(cityId, subcategoryId, businessSlug),
      async () => {
        const queryResult = await query(`
          SELECT l.*, c.name as category_name, c.icon_slug as category_icon, c.description as category_description
          FROM listings l
          LEFT JOIN categories c ON l.category_primary_id = c.id
          WHERE l.city_id = $1
          AND l.category_primary_id = $2
          AND l.slug = $3
          AND l.listing_status = 'active'
          AND l.deleted_at IS NULL
        `, [cityId, subcategoryId, businessSlug]);
        return { data: queryResult.rows[0] || null, error: null };
      },
      cacheTTL.business
    );
    return result;
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Update view count for a listing
export async function incrementViewCount(listingId: string) {
  try {
    await query(`
      UPDATE listings
      SET view_count = COALESCE(view_count, 0) + 1
      WHERE id = $1
    `, [listingId]);
    return { error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { error };
  }
}

// Update click count for a listing
export async function incrementClickCount(listingId: string, clickType: string) {
  try {
    let updateField: string;
    switch (clickType) {
      case 'website':
        updateField = 'click_through_count_website';
        break;
      case 'phone':
        updateField = 'click_through_count_phone';
        break;
      case 'facebook':
        updateField = 'click_through_count_facebook';
        break;
      case 'instagram':
        updateField = 'click_through_count_instagram';
        break;
      case 'twitter':
        updateField = 'click_through_count_twitter';
        break;
      case 'linkedin':
        updateField = 'click_through_count_linkedin';
        break;
      case 'whatsapp':
        updateField = 'click_through_count_whatsapp';
        break;
      case 'youtube':
        updateField = 'click_through_count_youtube';
        break;
      case 'tiktok':
        updateField = 'click_through_count_tiktok';
        break;
      default:
        return { error: new Error('Invalid click type') };
    }

    await query(`
      UPDATE listings
      SET ${updateField} = COALESCE(${updateField}, 0) + 1,
          updated_at = NOW()
      WHERE id = $1
    `, [listingId]);
    return { error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { error };
  }
}

// Insert a new listing (with cache invalidation)
export async function insertListing(listingData: any) {
  try {
    const result = await query(`
      INSERT INTO listings (
        slug, business_name, display_name, city_id, category_primary_id,
        description_short, description_long, contact_info, address_full,
        languages_spoken, services_offered_keywords, price_range,
        owner_is_expat, pet_friendly, kid_friendly, listing_status,
        is_verified_expatslist, is_pinned, view_count,
        click_through_count_website, click_through_count_phone,
        click_through_count_facebook, click_through_count_instagram,
        click_through_count_twitter, click_through_count_linkedin,
        click_through_count_whatsapp, click_through_count_youtube,
        click_through_count_tiktok, thumbs_up_count, thumbs_down_count,
        main_image_path, image_gallery_paths, owner_user_id
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15,
        $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30,
        $31, $32, $33
      ) RETURNING *
    `, [
      listingData.slug,
      listingData.business_name,
      listingData.display_name,
      listingData.city_id,
      listingData.category_primary_id,
      listingData.description_short,
      listingData.description_long,
      JSON.stringify(listingData.contact_info),
      listingData.address_full,
      listingData.languages_spoken,
      listingData.services_offered_keywords,
      listingData.price_range,
      listingData.owner_is_expat,
      listingData.pet_friendly,
      listingData.kid_friendly,
      listingData.listing_status,
      listingData.is_verified_expatslist,
      listingData.is_pinned,
      listingData.view_count,
      listingData.click_through_count_website,
      listingData.click_through_count_phone,
      listingData.click_through_count_facebook,
      listingData.click_through_count_instagram,
      listingData.click_through_count_twitter,
      listingData.click_through_count_linkedin,
      listingData.click_through_count_whatsapp,
      listingData.click_through_count_youtube,
      listingData.click_through_count_tiktok,
      listingData.thumbs_up_count,
      listingData.thumbs_down_count,
      listingData.main_image_path,
      listingData.image_gallery_paths,
      listingData.owner_user_id
    ]);

    // Invalidate relevant caches
    if (result.rows[0]) {
      // Get parent category for cache invalidation
      const categoryResult = await query('SELECT parent_id FROM categories WHERE id = $1', [listingData.category_primary_id]);
      const parentCategoryId = categoryResult.rows[0]?.parent_id;

      if (parentCategoryId) {
        // Use the cache invalidation helper
        const { invalidateCache } = await import('./cache');
        invalidateCache.onNewListing(listingData.city_id, parentCategoryId, listingData.category_primary_id);
        
        // Auto-update city listing count
        await updateCityListingCounts();
      }
    }

    return { data: result.rows[0], error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Update listing status (with cache invalidation)
export async function updateListingStatus(listingId: string, status: string) {
  try {
    // Get listing info for cache invalidation
    const listingResult = await query(`
      SELECT city_id, category_primary_id FROM listings WHERE id = $1
    `, [listingId]);

    await query(`
      UPDATE listings
      SET listing_status = $1,
          last_updated_by_admin_at = NOW()
      WHERE id = $2
    `, [status, listingId]);

    // Invalidate relevant caches
    if (listingResult.rows[0]) {
      const { city_id, category_primary_id } = listingResult.rows[0];

      // Get parent category for cache invalidation
      const categoryResult = await query('SELECT parent_id FROM categories WHERE id = $1', [category_primary_id]);
      const parentCategoryId = categoryResult.rows[0]?.parent_id;

      if (parentCategoryId) {
        // Use the cache invalidation helper
        const { invalidateCache } = await import('./cache');
        invalidateCache.onListingStatusChange(city_id, parentCategoryId, category_primary_id);
        
        // Auto-update city listing count
        await updateCityListingCounts();
      }
    }

    return { error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { error };
  }
}

// Get listing by ID
export async function getListingById(listingId: string) {
  try {
    const result = await query(`
      SELECT * FROM listings WHERE id = $1
    `, [listingId]);
    return { data: result.rows[0] || null, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Update thumbs up/down count
export async function updateThumbsCount(listingId: string, direction: 'up' | 'down') {
  try {
    const field = direction === 'up' ? 'thumbs_up_count' : 'thumbs_down_count';
    const result = await query(`
      UPDATE listings
      SET ${field} = COALESCE(${field}, 0) + 1
      WHERE id = $1
      RETURNING thumbs_up_count, thumbs_down_count
    `, [listingId]);
    return { data: result.rows[0], error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

// Update city listing counts
export async function updateCityListingCounts() {
  try {
    await query(`
      UPDATE cities
      SET
        total_listing_count = (
          SELECT COUNT(*)
          FROM listings
          WHERE city_id = cities.id
          AND listing_status = 'active'
          AND deleted_at IS NULL
        ),
        verified_listing_count = (
          SELECT COUNT(*)
          FROM listings
          WHERE city_id = cities.id
          AND listing_status = 'active'
          AND is_verified_expatslist = true
          AND deleted_at IS NULL
        )
    `);

    // Invalidate cities cache after updating counts
    const { cache, cacheKeys } = await import('./cache');
    cache.delete(cacheKeys.cities());
    cache.delete(cacheKeys.totalBusinessCount());

    return { error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { error };
  }
}

// Update city classified counts
export async function updateCityClassifiedCounts(citySlug?: string) {
  try {
    let sql = `
      UPDATE cities
      SET
        total_classified_count = (
          SELECT COUNT(*)
          FROM classified_posts
          WHERE city_slug = cities.path_slug
          AND is_active = true
          AND (expires_at IS NULL OR expires_at > NOW())
        ),
        updated_at = NOW()
    `;

    const params: any[] = [];

    if (citySlug) {
      sql += ` WHERE path_slug = $1`;
      params.push(citySlug);
    }

    await query(sql, params);

    // Invalidate relevant caches
    const { cache, cacheKeys } = await import('./cache');
    cache.delete(cacheKeys.cities());

    return { error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { error };
  }
}

// Get real-time total business count (bypasses cache for admin/critical operations)
export async function getRealTimeBusinessCount() {
  try {
    const result = await query("SELECT COUNT(*) as total_businesses FROM listings WHERE listing_status = 'active' AND deleted_at IS NULL");
    return { data: result.rows[0]?.total_businesses || 0, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: 0, error };
  }
}

// Password management functions for direct database access
export async function getUserPasswordHash(userId: string) {
  try {
    const result = await query('SELECT id, email, encrypted_password FROM auth.users WHERE id = $1', [userId]);
    return { data: result.rows[0] || null, error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { data: null, error };
  }
}

export async function updateUserPasswordHash(userId: string, newPasswordHash: string) {
  try {
    await query('UPDATE auth.users SET encrypted_password = $1, updated_at = NOW() WHERE id = $2', [newPasswordHash, userId]);
    return { error: null };
  } catch (error) {
    console.error('Database error:', error);
    return { error };
  }
}


