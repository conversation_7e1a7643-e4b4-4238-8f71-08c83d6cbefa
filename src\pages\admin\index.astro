---
import AdminLayout from '../../layouts/AdminLayout.astro';
import { query } from '../../lib/database';

export const prerender = false;

// No server-side auth - handled client-side for cross-environment compatibility

// Get dashboard statistics using direct database queries
const totalListingsResult = await query('SELECT COUNT(*) as count FROM listings WHERE deleted_at IS NULL');
const totalListings = parseInt(totalListingsResult.rows[0].count);

const pendingListingsResult = await query('SELECT COUNT(*) as count FROM listings WHERE listing_status = $1 AND deleted_at IS NULL', ['pending_approval']);
const pendingListings = parseInt(pendingListingsResult.rows[0].count);

const verifiedListingsResult = await query('SELECT COUNT(*) as count FROM listings WHERE is_verified_expatslist = true AND deleted_at IS NULL');
const verifiedListings = parseInt(verifiedListingsResult.rows[0].count);

const totalCitiesResult = await query('SELECT COUNT(*) as count FROM cities');
const totalCities = parseInt(totalCitiesResult.rows[0].count);

// Get recent listings for review with city and category names
const recentListingsResult = await query(`
  SELECT l.*,
         c.name as city_name,
         c.country as city_country,
         cat.name as category_name
  FROM listings l
  LEFT JOIN cities c ON l.city_id = c.id
  LEFT JOIN categories cat ON l.category_primary_id = cat.id
  WHERE l.listing_status = $1
  AND l.deleted_at IS NULL
  ORDER BY l.created_at DESC
  LIMIT 10
`, ['pending_approval']);
const recentListings = recentListingsResult.rows;

// Get flagged listings with city and category names
const flaggedListingsResult = await query(`
  SELECT l.*,
         c.name as city_name,
         c.country as city_country,
         cat.name as category_name
  FROM listings l
  LEFT JOIN cities c ON l.city_id = c.id
  LEFT JOIN categories cat ON l.category_primary_id = cat.id
  WHERE l.thumbs_down_count > 5
  AND l.deleted_at IS NULL
  ORDER BY l.thumbs_down_count DESC
  LIMIT 5
`);
const flaggedListings = flaggedListingsResult.rows;

// Get recent admin notifications
let recentNotifications = [];
try {
  const notificationsResult = await query(`
    SELECT
      id,
      type,
      title,
      message,
      data,
      read,
      created_at
    FROM admin_notifications
    WHERE read = false
    ORDER BY created_at DESC
    LIMIT 5
  `);
  recentNotifications = notificationsResult.rows;
} catch (e) {
  console.error('Error fetching notifications:', e);
}

const stats = {
  totalListings: totalListings || 0,
  pendingListings: pendingListings || 0,
  verifiedListings: verifiedListings || 0,
  totalCities: totalCities || 0,
  unreadNotifications: recentNotifications.length || 0
};
---

<AdminLayout title="Dashboard">
  <!-- Page Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
    <p class="text-gray-600 mt-2">Overview of ExpatsList platform - Updated v2.1</p>
  </div>

  <!-- Stats Grid -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">📋</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Total Listings</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.totalListings}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">⏳</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Pending Approval</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.pendingListings}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">✅</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Verified</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.verifiedListings}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">🌍</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Cities</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.totalCities}</p>
        </div>
      </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-6">
      <div class="flex items-center">
        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
          <span class="text-2xl">🔔</span>
        </div>
        <div class="ml-4">
          <h3 class="text-sm font-medium text-gray-600">Notifications</h3>
          <p class="text-2xl font-bold text-gray-900">{stats.unreadNotifications}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
    <!-- Pending Listings -->
    <div class="bg-white rounded-xl shadow-md">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">Pending Listings</h2>
          <a href="/admin/listings?status=pending" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View All →
          </a>
        </div>
      </div>

      <div class="p-6">
        {recentListings && recentListings.length > 0 ? (
          <div class="space-y-4">
            {recentListings.slice(0, 5).map((listing) => {
              // Use display_name only if it's meaningful (not just first word of business_name)
              const displayName = (listing.display_name && listing.display_name.length > 3 && !listing.business_name.startsWith(listing.display_name + ' '))
                ? listing.display_name
                : listing.business_name;

              return (
              <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="flex-1 cursor-pointer" onclick={`window.location.href='/admin/listing/${listing.id}'`}>
                  <h3 class="font-medium text-gray-900 hover:text-blue-600">{displayName}</h3>
                  <p class="text-sm text-gray-600">{listing.city_name} • {listing.category_name}</p>
                  <div class="flex items-center space-x-3 mt-1">
                    <p class="text-xs text-gray-500">
                      {new Date(listing.created_at).toLocaleDateString()}
                    </p>
                    {listing.main_image_path && (
                      <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">📷 Has Photos</span>
                    )}
                    {listing.owner_user_id && (
                      <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">👤 Registered User</span>
                    )}
                  </div>
                  <p class="text-xs text-gray-600 mt-1 truncate">{listing.description_short}</p>
                </div>
                <div class="flex flex-col space-y-2 ml-4">
                  <button
                    onclick={`event.stopPropagation(); approveListing('${listing.id}')`}
                    class="px-3 py-1 text-xs font-medium text-white bg-green-600 rounded hover:bg-green-700"
                  >
                    Approve
                  </button>
                  <button
                    onclick={`event.stopPropagation(); rejectListing('${listing.id}')`}
                    class="px-3 py-1 text-xs font-medium text-white bg-red-600 rounded hover:bg-red-700"
                  >
                    Reject
                  </button>
                  <button
                    onclick={`event.stopPropagation(); window.location.href='/admin/listing/${listing.id}'`}
                    class="px-3 py-1 text-xs font-medium text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
                  >
                    View Details
                  </button>
                </div>
              </div>
            );
            })}
          </div>
        ) : (
          <p class="text-gray-500 text-center py-8">No pending listings</p>
        )}
      </div>
    </div>

    <!-- Flagged Content -->
    <div class="bg-white rounded-xl shadow-md">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">Flagged Content</h2>
          <a href="/admin/listings?flagged=true" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View All →
          </a>
        </div>
      </div>

      <div class="p-6">
        {flaggedListings && flaggedListings.length > 0 ? (
          <div class="space-y-4">
            {flaggedListings.map((listing) => {
              // Use display_name only if it's meaningful (not just first word of business_name)
              const displayName = (listing.display_name && listing.display_name.length > 3 && !listing.business_name.startsWith(listing.display_name + ' '))
                ? listing.display_name
                : listing.business_name;

              return (
              <div class="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                <div class="flex-1">
                  <h3 class="font-medium text-gray-900">{displayName}</h3>
                  <p class="text-sm text-gray-600">{listing.city_name} • {listing.category_name}</p>
                  <p class="text-xs text-red-600">
                    👎 {listing.thumbs_down_count} flags
                  </p>
                </div>
                <div class="flex space-x-2">
                  <a
                    href={`/admin/listings/${listing.id}`}
                    class="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded hover:bg-blue-200"
                  >
                    Review
                  </a>
                </div>
              </div>
            );
            })}
          </div>
        ) : (
          <p class="text-gray-500 text-center py-8">No flagged content</p>
        )}
      </div>
    </div>

    <!-- Recent Notifications -->
    <div class="bg-white rounded-xl shadow-md">
      <div class="p-6 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold text-gray-900">Recent Notifications</h2>
          <span class="text-sm text-gray-500">
            {stats.unreadNotifications} unread
          </span>
        </div>
      </div>

      <div class="p-6">
        {recentNotifications && recentNotifications.length > 0 ? (
          <div class="space-y-4">
            {recentNotifications.map((notification) => {
              const data = notification.data || {};
              const isVerificationPayment = notification.type === 'verification_payment';
              const isBusinessSubmission = notification.type === 'new_business_submission';

              return (
                <div class={`p-4 rounded-lg border-l-4 ${
                  isVerificationPayment
                    ? 'border-l-green-500 bg-green-50'
                    : isBusinessSubmission
                      ? 'border-l-blue-500 bg-blue-50'
                      : 'border-l-gray-500 bg-gray-50'
                }`}>
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <div class="flex items-center space-x-2 mb-1">
                        <span class="text-lg">
                          {isVerificationPayment ? '💳' : isBusinessSubmission ? '🏢' : '📢'}
                        </span>
                        <h3 class="font-medium text-gray-900 text-sm">{notification.title}</h3>
                      </div>
                      <p class="text-xs text-gray-600 mb-2">{notification.message}</p>

                      {data.hasVerificationPayment !== undefined && (
                        <span class={`text-xs px-2 py-1 rounded-full ${
                          data.hasVerificationPayment
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {data.hasVerificationPayment ? 'PAID VERIFICATION' : 'FREE LISTING'}
                        </span>
                      )}
                    </div>
                    <div class="text-xs text-gray-500">
                      {new Date(notification.created_at).toLocaleDateString()}
                    </div>
                  </div>

                  {isBusinessSubmission && data.listingId && (
                    <div class="mt-3 flex space-x-2">
                      <button
                        onclick={`approveListing('${data.listingId}')`}
                        class="px-2 py-1 text-xs font-medium text-white bg-green-600 rounded hover:bg-green-700"
                      >
                        Approve
                      </button>
                      <button
                        onclick={`window.location.href='/admin/listing/${data.listingId}'`}
                        class="px-2 py-1 text-xs font-medium text-blue-600 border border-blue-600 rounded hover:bg-blue-50"
                      >
                        Review Details
                      </button>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        ) : (
          <p class="text-gray-500 text-center py-8">No recent notifications</p>
        )}
      </div>
    </div>
  </div>

  <!-- Quick Tools -->
  <div class="bg-white rounded-xl shadow-md p-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Tools</h2>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <button
        onclick="updateCityCounts()"
        class="p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
      >
        <div class="text-2xl mb-2">🔄</div>
        <h3 class="font-medium text-gray-900">Update City Counts</h3>
        <p class="text-sm text-gray-600">Refresh denormalized listing counts</p>
      </button>

      <a
        href="/admin/listings/new"
        class="p-4 text-left border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50 transition-colors"
      >
        <div class="text-2xl mb-2">➕</div>
        <h3 class="font-medium text-gray-900">Add New Listing</h3>
        <p class="text-sm text-gray-600">Manually create a business listing</p>
      </a>

      <a
        href="/admin/cities/new"
        class="p-4 text-left border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50 transition-colors"
      >
        <div class="text-2xl mb-2">🌍</div>
        <h3 class="font-medium text-gray-900">Add New City</h3>
        <p class="text-sm text-gray-600">Add a new city to the platform</p>
      </a>
    </div>
  </div>
</AdminLayout>

<script is:inline>
  // Admin verification
  document.addEventListener('DOMContentLoaded', async () => {
    let attempts = 0;
    while (!window.authFunctions && attempts < 30) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }

    if (!window.authFunctions) {
      window.location.href = '/account?error=admin_access_required';
      return;
    }

    const currentUser = await window.authFunctions.getCurrentUser();
    if (!currentUser || currentUser.email !== '<EMAIL>') {
      window.location.href = '/account?error=admin_access_required';
      return;
    }
  });

  async function approveListing(listingId) {
    try {
      const response = await fetch('/api/admin/approve-listing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ listingId })
      });

      const result = await response.json();

      if (result.success) {
        location.reload();
      } else {
        alert('Error approving listing: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error approving listing');
    }
  }

  function rejectListing(listingId) {
    // Redirect to the detailed view where rejection can be handled properly
    window.location.href = `/admin/listing/${listingId}`;
  }

  async function updateCityCounts() {
    try {
      const response = await fetch('/api/admin/update-counts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();

      if (result.success) {
        alert('City counts updated successfully!');
        location.reload();
      } else {
        alert('Error updating counts: ' + (result.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error updating counts');
    }
  }
</script>
