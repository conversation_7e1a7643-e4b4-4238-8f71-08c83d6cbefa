import type { APIRoute } from 'astro';
import { updateListingStatus, query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { listingId, rejectionReason } = await request.json();
    console.log('Reject listing request - Listing ID:', listingId, 'Reason:', rejectionReason);

    if (!listingId) {
      return new Response(JSON.stringify({ error: 'Missing listing ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get listing details for notification
    const listingResult = await query(`
      SELECT l.business_name, l.owner_user_id, u.email as owner_email
      FROM listings l
      LEFT JOIN auth.users u ON l.owner_user_id = u.id
      WHERE l.id = $1
    `, [listingId]);

    const listing = listingResult.rows[0];

    // Update listing status to rejected
    const { error } = await updateListingStatus(listingId, 'rejected');

    if (error) {
      console.error('Error rejecting listing:', error);
      return new Response(JSON.stringify({ error: 'Failed to reject listing' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Create admin notification for rejection
    if (listing && rejectionReason) {
      try {
        await query(`
          INSERT INTO admin_notifications (
            type,
            title,
            message,
            data,
            created_at
          ) VALUES ($1, $2, $3, $4, NOW())
        `, [
          'business_rejection',
          'Business Listing Rejected',
          `${listing.business_name} has been rejected. Reason: ${rejectionReason}`,
          JSON.stringify({
            listingId,
            businessName: listing.business_name,
            rejectionReason,
            ownerEmail: listing.owner_email
          })
        ]);
      } catch (notificationError) {
        console.log('Admin notification failed:', notificationError);
      }
    }

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
