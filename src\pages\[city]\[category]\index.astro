---
// Category listing page: /playa-del-carmen/housing-accommodation/
import Layout from '../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../components/MinimalAcctButton.astro';
import ProtectedContact from '../../../components/ProtectedContact.astro';
import Pagination from '../../../components/Pagination.astro';
import { getCityBySlug, getCategories, getListingsForCategory } from '../../../lib/database';
import Breadcrumb from '../../../components/Breadcrumb.astro';

// Set cache headers for better performance
Astro.response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=600'); // 5 min browser, 10 min CDN

const { city: citySlug, category: categorySlug } = Astro.params;

if (!citySlug || !categorySlug) {
  return Astro.redirect('/cities');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/cities');
}

// Get categories from database
const { data: allCategories } = await getCategories();
const category = allCategories?.find(cat => cat.slug === categorySlug && cat.parent_id === null);

if (!category) {
  return Astro.redirect(`/${citySlug}`);
}

// Icon mapping function for main categories
function getIconForCategory(slug: string): string {
  const iconMap: Record<string, string> = {
    'housing-relocation': '🏠',
    'health-wellness': '🧑‍⚕️',
    'food-dining': '🍽️',
    'professional-legal-financial': '💼',
    'everyday-services-repairs': '🛠️',
    'shopping-leisure-community': '🛍️',
    'education-childcare': '🎓'
  };
  return iconMap[slug] || '📁';
}

// Get category-specific icon for fallbacks
function getCategoryIcon(subcategorySlug: string): string {
  const iconMap: Record<string, string> = {
    // Housing & Relocation
    'rental-agencies': '🏢',
    'real-estate-agents': '🏘️',
    'property-management': '🏠',
    'moving-shipping-companies': '📦',
    'car-rental': '🚗',
    'furniture-stores': '🛋️',

    // Health & Wellness
    'medical-services': '🏥',
    'emergency-medical-services': '🚑',
    'pharmacies': '💊',
    'mental-health-therapy': '🧠',
    'alternative-complementary-medicine': '🌿',
    'fitness-gyms-personal-trainers': '💪',
    'massage-spas-beauty-salons': '💆',
    'yoga-meditation-studios': '🧘',

    // Food & Dining
    'restaurants': '🍽️',
    'cafes-coffee-shops': '☕',
    'grocery-stores-specialty-foods': '🛒',
    'local-food-markets-street-food': '🥬',

    // Professional, Legal & Financial
    'legal-immigration-services': '⚖️',
    'financial-services': '💰',
    'insurance-brokers': '🛡️',
    'business-support': '💼',
    'coworking-spaces': '💻',
    'currency-exchange': '💱',

    // Everyday Services & Repairs
    'connectivity': '📶',
    'home-maintenance-repair': '🔧',
    'appliance-repair': '🔌',
    'clothing-shoe-repair-alterations': '👕',
    'cleaning-services': '🧽',
    'gardeners-pool-maintenance': '🌱',
    'pet-services': '🐕',
    'vehicle-services': '🚗',
    'locksmiths': '🔑',

    // Shopping, Leisure & Community
    'retail-shopping': '🛍️',
    'tour-agencies': '🗺️',
    'bars-nightclubs': '🍻',
    'home-improvement': '🔨',
    'extra-curricular-activities': '🎨',
    'community-social': '👥',

    // Private Education & Childcare
    'schools': '🏫',
    'preschools-daycares': '👶',
    'language-schools': '📚',
    'after-school-programs-tutoring-workshops': '🎓'
  };
  return iconMap[subcategorySlug] || '🏪';
}

// Pagination setup
const url = new URL(Astro.request.url);
const currentPage = parseInt(url.searchParams.get('page') || '1');
const itemsPerPage = 10;

// Get listings for this category (main category, not subcategories) using direct database connection
const { data: allListings, error: listingsError } = await getListingsForCategory(city.id, category.id);

// Implement pagination
const totalListings = allListings?.length || 0;
const totalPages = Math.ceil(totalListings / itemsPerPage);
const startIndex = (currentPage - 1) * itemsPerPage;
const endIndex = startIndex + itemsPerPage;
const listings = allListings?.slice(startIndex, endIndex) || [];

console.log('Category page debug:', {
  cityId: city.id,
  categoryId: category.id,
  categoryName: category.name,
  listingsCount: listings?.length,
  listingsError,
  firstListing: listings?.[0] ? {
    id: listings[0].id,
    business_name: listings[0].business_name,
    category_name: listings[0].category_name,
    category_slug: listings[0].category_slug,
    main_category_name: listings[0].main_category_name,
    main_category_slug: listings[0].main_category_slug
  } : null
});

const pageTitle = `${category.name} in ${city.name} - ExpatsList`;
const pageDescription = `Find the best ${category.name.toLowerCase()} businesses in ${city.name}. Trusted recommendations from fellow expats.`;
const canonicalUrl = `https://expatslist.org/${citySlug}/${categorySlug}`;

// Helper function for category icons
function getIconForCategory(slug: string): string {
  const iconMap: Record<string, string> = {
    'housing-relocation': '🏠',
    'health-wellness': '🧑‍⚕️',
    'food-dining': '🍽️',
    'professional-legal-financial': '💼',
    'everyday-services-repairs': '🛠️',
    'shopping-leisure-community': '🛍️',
    'private-education-childcare': '🎓'
  };
  return iconMap[slug] || '🏢';
}
---

<Layout title={pageTitle} description={pageDescription} canonical={canonicalUrl}>
  <!-- Minimal Account Button -->
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Compact Category Header -->
    <div class="bg-white border-b border-slate-200 py-4">
      <div class="max-w-7xl mx-auto px-6">
        <div class="flex items-center justify-between">
          <!-- Left: Breadcrumb Only -->
          <div class="flex items-center space-x-4">
            <Breadcrumb
              items={[
                { label: 'Main', href: '/', icon: '🌎' },
                { label: city.name, href: `/${citySlug}`, icon: '📍' },
                { label: category.name, isActive: true }
              ]}
            />
          </div>
        </div>
      </div>
    </div>



    <!-- Business Listings Section -->
    <div class="bg-white py-12">
      <div class="max-w-7xl mx-auto px-6">
        {listings && listings.length > 0 ? (
          <div>
            <!-- Modern Business Cards Grid - Single Row Layout -->
            <div class="grid grid-cols-1 gap-6">
              {listings.map((listing, index) => {
                // Use display_name only if it's meaningful (not just first word of business_name)
                const displayName = (listing.display_name && listing.display_name.length > 3 && !listing.business_name.startsWith(listing.display_name + ' '))
                  ? listing.display_name
                  : listing.business_name;

                // Get the specific icon for this listing's subcategory
                const listingIcon = getCategoryIcon(listing.category_slug || '');

                return (
                <div class="relative rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group h-48">
                  <!-- Business-branded background with blur overlay -->
                  {(listing.main_image_path || listing.google_photo_1) ? (
                    <>
                      <div
                        class="absolute inset-0 bg-cover bg-center transform group-hover:scale-105 transition-transform duration-500"
                        style={`background-image: url('${listing.main_image_path || `/api/image-proxy?url=${encodeURIComponent(listing.google_photo_1)}`}');`}
                      ></div>
                      <!-- Stronger overlay layers for better text readability -->
                      <div class="absolute inset-0 backdrop-blur-[2px] bg-white/20 group-hover:bg-white/15 transition-all duration-300"></div>
                      <div class="absolute inset-0 bg-gradient-to-r from-slate-900/60 via-slate-900/40 to-slate-900/60 group-hover:from-slate-900/70 group-hover:via-slate-900/50 group-hover:to-slate-900/70 transition-all duration-300"></div>
                    </>
                  ) : (
                    <>
                      <!-- Fallback gradient background -->
                      <div class="absolute inset-0 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700"></div>
                      <div class="absolute inset-0 bg-gradient-to-r from-slate-900/60 via-slate-900/40 to-slate-900/60"></div>
                    </>
                  )}

                  <!-- Content overlay -->
                  <div class="relative h-full p-6 flex items-center">
                    <div class="flex items-center gap-6 w-full">
                      <!-- Left: Business Photo or Icon -->
                      <div class="w-20 h-16 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg overflow-hidden bg-white/20 backdrop-blur-sm">
                        {(listing.main_image_path || listing.google_photo_1) ? (
                          <>
                            <img
                              src={listing.main_image_path || `/api/image-proxy?url=${encodeURIComponent(listing.google_photo_1)}`}
                              alt={`${displayName} - Main Photo`}
                              class="w-full h-full object-cover rounded-xl hover:scale-105 transition-transform duration-200"
                              loading="lazy"
                              onerror={`this.style.display='none'; ${!listing.main_image_path && listing.google_photo_2 ? 'this.nextElementSibling.style.display=\'block\';' : 'this.nextElementSibling.nextElementSibling.style.display=\'flex\';'}`}
                            />
                            {!listing.main_image_path && listing.google_photo_2 && (
                              <img
                                src={`/api/image-proxy?url=${encodeURIComponent(listing.google_photo_2)}`}
                                alt={`${displayName} - Second Photo`}
                                class="w-full h-full object-cover rounded-xl hover:scale-105 transition-transform duration-200"
                                loading="lazy"
                                style="display: none;"
                                onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                              />
                            )}
                            <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center" style="display: none;">
                              <span class="text-xl text-white">{listingIcon}</span>
                            </div>
                          </>
                        ) : (
                          <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                            <span class="text-xl text-white">{listingIcon}</span>
                          </div>
                        )}
                      </div>

                      <!-- Right: Main Content -->
                      <div class="flex-1 min-w-0">
                        <!-- Business Name and Category -->
                        <div class="mb-3">
                          <h3 class="text-lg font-bold text-white group-hover:text-blue-200 transition-colors leading-tight mb-1" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1), -1px -1px 0px rgba(0,0,0,0.7), 1px -1px 0px rgba(0,0,0,0.7), -1px 1px 0px rgba(0,0,0,0.7), 1px 1px 0px rgba(0,0,0,0.7);">
                            {listing.category_slug && listing.slug ? (
                              <a href={`/${citySlug}/${categorySlug}/${listing.category_slug}/${listing.slug}`} class="hover:underline">
                                <span class="bg-black/40 px-2 py-1 rounded-md backdrop-blur-sm">{displayName}</span>
                              </a>
                            ) : (
                              <span class="bg-black/40 px-2 py-1 rounded-md backdrop-blur-sm">{displayName}</span>
                            )}
                          </h3>
                          {listing.subcategory_name && (
                            <div class="text-sm text-white font-medium" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1);">
                              <span class="bg-black/30 px-2 py-1 rounded-md backdrop-blur-sm">{listing.subcategory_name}</span>
                            </div>
                          )}
                        </div>

                        <!-- Badges Row -->
                        <div class="flex flex-wrap gap-2 mb-3">
                          {/* Show price for relevant categories */}
                          {(category.slug.includes('housing-relocation') || category.slug.includes('food-dining') || category.slug.includes('massage-spas-beauty-salons') || category.slug.includes('restaurants') || category.slug.includes('cafes-coffee-shops')) && listing.price_range && (
                            <span class="px-2 py-1 bg-emerald-500/80 text-white text-xs font-semibold rounded-full border border-emerald-400/50 backdrop-blur-sm">
                              💰 {listing.price_range === '$' ? 'Budget' : listing.price_range === '$$' ? 'Mid-range' : listing.price_range === '$$$' ? 'Premium' : listing.price_range}
                            </span>
                          )}
                          {listing.is_verified_expatslist && (
                            <span class="px-2 py-1 bg-blue-500/80 text-white text-xs font-semibold rounded-full border border-blue-400/50 backdrop-blur-sm">
                              ✓ Verified
                            </span>
                          )}
                        </div>

                        <!-- Address with Pin -->
                        {listing.address_full && (
                          <div class="flex items-center mb-3">
                            <span class="text-white mr-2" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1);">📍</span>
                            <span class="text-sm text-white" style="text-shadow: 2px 2px 4px rgba(0,0,0,0.9), 1px 1px 2px rgba(0,0,0,1);">
                              <span class="bg-black/30 px-2 py-1 rounded-md backdrop-blur-sm">{listing.address_full}</span>
                            </span>
                          </div>
                        )}

                        <!-- Admin Controls & Contact Methods and Features Row -->
                        <div class="flex flex-wrap items-center gap-2 mb-4">
                          {/* Admin Delete Button (hidden by default) */}
                          <button
                            class="admin-delete-business-btn hidden px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                            data-business-id={listing.id}
                            data-business-name={displayName}
                            onclick="deleteBusinessListing(this)"
                          >
                            Delete
                          </button>

                          {/* Protected Contact Pills */}
                          <ProtectedContact
                            phone={listing.contact_info?.phone}
                            email={listing.contact_info?.email}
                            whatsapp={listing.contact_info?.whatsapp}
                            type="business"
                            size="small"
                            style="pill"
                          />
                          {listing.contact_info?.website && (
                            <span class="bg-green-500/70 text-white px-2 py-1 rounded-full text-xs font-medium border border-green-400/50 backdrop-blur-sm">
                              🌐 Website
                            </span>
                          )}

                          {/* Feature Pills */}
                          {listing.pet_friendly && (
                            <span class="px-2 py-1 bg-amber-500/70 text-white text-xs font-medium rounded-full border border-amber-400/50 backdrop-blur-sm">
                              🐕 Pet Friendly
                            </span>
                          )}
                          {listing.kid_friendly && (
                            <span class="px-2 py-1 bg-pink-500/70 text-white text-xs font-medium rounded-full border border-pink-400/50 backdrop-blur-sm">
                              👶 Kid Friendly
                            </span>
                          )}
                          {listing.owner_is_expat && (
                            <span class="px-2 py-1 bg-purple-500/70 text-white text-xs font-medium rounded-full border border-purple-400/50 backdrop-blur-sm">
                              🌍 Expat Owned
                            </span>
                          )}
                          {listing.languages_spoken && listing.languages_spoken.includes('English') && (
                            <span class="px-2 py-1 bg-blue-500/70 text-white text-xs font-medium rounded-full border border-blue-400/50 backdrop-blur-sm">
                              🇺🇸 English
                            </span>
                          )}
                        </div>

                        <!-- Bottom Row: Stats and CTA -->
                        <div class="flex items-center justify-between">
                          <div class="flex items-center space-x-2 text-xs">
                            {listing.view_count > 0 && (
                              <span class="bg-black/50 text-white px-2 py-1 rounded-full backdrop-blur-sm border border-white/20" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.9);">👁️ {listing.view_count}</span>
                            )}
                            {listing.click_through_count_website > 0 && (
                              <span class="bg-black/50 text-white px-2 py-1 rounded-full backdrop-blur-sm border border-white/20" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.9);">🌐 {listing.click_through_count_website}</span>
                            )}
                          </div>
                          {listing.category_slug && listing.slug ? (
                            <a
                              href={`/${citySlug}/${categorySlug}/${listing.category_slug}/${listing.slug}`}
                              class="group px-4 py-2 bg-white/90 hover:bg-white text-slate-900 text-sm font-semibold rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl backdrop-blur-sm"
                            >
                              <span class="group-hover:scale-105 transition-transform inline-block">View Details →</span>
                            </a>
                          ) : (
                            <span class="text-white/60 text-xs">Details unavailable</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                );
              })}
            </div>

            <!-- World-Class Pagination -->
            <div class="mt-12">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                baseUrl={`/${citySlug}/${categorySlug}`}
                totalItems={totalListings}
                itemsPerPage={itemsPerPage}
                itemName={`${category.name.toLowerCase()} businesses in ${city.name}`}
              />
            </div>
        </div>
      ) : (
        <!-- Premium Empty State -->
        <div class="text-center py-20">
          <div class="bg-white rounded-3xl shadow-2xl border border-slate-200/50 p-12 max-w-2xl mx-auto">
            <div class="w-32 h-32 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-8">
              <span class="text-6xl">{getIconForCategory(category.slug)}</span>
            </div>
            <h2 class="text-4xl font-bold text-slate-900 mb-6">No businesses yet</h2>
            <p class="text-xl text-slate-600 mb-10 leading-relaxed">
              Be the first to add a {category.name.toLowerCase()} business in {city.name} and help fellow expats discover great local services.
            </p>
            <a
              href="/add-your-business"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-bold rounded-2xl transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
            >
              <span class="mr-3 text-xl">✨</span>
              Add Your Business
            </a>
          </div>
        </div>
      )}
    </div>

    <!-- Premium Back Navigation -->
    <div class="max-w-7xl mx-auto px-6 pb-12">
      <div class="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-8">
        <div class="text-center">
          <a
            href={`/${citySlug}`}
            class="group inline-flex items-center px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <span class="group-hover:scale-105 transition-transform">← Back to All Categories</span>
          </a>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script is:inline define:vars={{ citySlug, categorySlug: category.slug }}>
  // Check if user is admin and show admin controls
  document.addEventListener('DOMContentLoaded', () => {
    function checkAdminStatus() {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      console.log('Business Category Admin check - User:', currentUser?.email, 'Profile:', userProfile?.role);

      if (currentUser && (userProfile?.role === 'administrator' || currentUser.email === '<EMAIL>')) {
        console.log('Admin detected, showing business delete buttons');
        // Show all admin delete buttons
        const deleteButtons = document.querySelectorAll('.admin-delete-business-btn');
        deleteButtons.forEach(btn => {
          btn.classList.remove('hidden');
        });
      }
    }

    // Try immediately
    checkAdminStatus();

    // Also try after a delay in case auth is still loading
    setTimeout(checkAdminStatus, 1000);

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAdminStatus);
  });

  // Global function to delete business listings
  window.deleteBusinessListing = async function(button) {
    const businessId = button.getAttribute('data-business-id');
    const businessName = button.getAttribute('data-business-name');

    const currentUser = window.authFunctions?.getCurrentUser();
    const userProfile = window.authFunctions?.getUserProfile();

    console.log('Delete attempt - User:', currentUser?.email, 'Profile:', userProfile?.role);

    if (!currentUser || (userProfile?.role !== 'administrator' && currentUser.email !== '<EMAIL>')) {
      // Show unauthorized state on button
      button.innerHTML = '❌ Unauthorized';
      button.classList.add('bg-red-800');
      setTimeout(() => {
        button.innerHTML = 'Delete';
        button.classList.remove('bg-red-800');
      }, 2000);
      return;
    }

    try {
      // Update button to show loading state
      button.disabled = true;
      button.innerHTML = '<span class="animate-spin">⏳</span> Deleting...';
      button.classList.add('opacity-75');

      const response = await fetch('/api/admin/delete-business', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId: businessId,
          adminUserId: currentUser.id
        })
      });

      const result = await response.json();

      if (response.ok) {
        // Show success state briefly
        button.innerHTML = '✅ Deleted';
        button.classList.remove('bg-red-600', 'hover:bg-red-700');
        button.classList.add('bg-green-600');

        // Animate removal after brief success feedback
        setTimeout(() => {
          const businessElement = button.closest('.relative');
          if (businessElement) {
            businessElement.style.transition = 'all 0.3s ease-out';
            businessElement.style.transform = 'translateX(-100%)';
            businessElement.style.opacity = '0';
            setTimeout(() => businessElement.remove(), 300);
          }
        }, 1000);
      } else {
        // Show error state
        button.innerHTML = '❌ Failed';
        button.classList.remove('bg-red-600');
        button.classList.add('bg-red-800');

        // Reset after 2 seconds
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = 'Delete';
          button.classList.remove('bg-red-800', 'opacity-75');
          button.classList.add('bg-red-600', 'hover:bg-red-700');
        }, 2000);
      }
    } catch (error) {
      console.error('Error deleting business:', error);

      // Show error state
      button.innerHTML = '❌ Error';
      button.classList.remove('bg-red-600');
      button.classList.add('bg-red-800');

      // Reset after 2 seconds
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = 'Delete';
        button.classList.remove('bg-red-800', 'opacity-75');
        button.classList.add('bg-red-600', 'hover:bg-red-700');
      }, 2000);
    }
  };
</script>

<script>
  // No additional JavaScript needed for price display
</script>
