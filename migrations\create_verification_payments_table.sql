-- Create verification_payments table to track PayPal payments for business verification
CREATE TABLE IF NOT EXISTS verification_payments (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    transaction_id VARCHAR(255) UNIQUE NOT NULL,
    payer_email VARCHAR(255) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    payment_status VARCHAR(50) NOT NULL,
    payment_date TIMESTAMP NOT NULL,
    ipn_data JSONB,
    processed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create admin_notifications table for admin alerts
CREATE TABLE IF NOT EXISTS admin_notifications (
    id SERIAL PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    read_at TIMESTAMP
);

-- Create user_activity_log table for tracking user activities
CREATE TABLE IF NOT EXISTS user_activity_log (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    activity_type VARCHAR(100) NOT NULL,
    activity_data JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_verification_payments_user_id ON verification_payments(user_id);
CREATE INDEX IF NOT EXISTS idx_verification_payments_transaction_id ON verification_payments(transaction_id);
CREATE INDEX IF NOT EXISTS idx_verification_payments_status ON verification_payments(payment_status);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_type ON admin_notifications(type);
CREATE INDEX IF NOT EXISTS idx_admin_notifications_read ON admin_notifications(read);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_user_id ON user_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_log_type ON user_activity_log(activity_type);

-- Add foreign key constraints if users table exists
-- ALTER TABLE verification_payments ADD CONSTRAINT fk_verification_payments_user_id 
--   FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
-- ALTER TABLE user_activity_log ADD CONSTRAINT fk_user_activity_log_user_id 
--   FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;
