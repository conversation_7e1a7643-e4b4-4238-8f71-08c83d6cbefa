---
import AdminLayout from '../../../layouts/AdminLayout.astro';
import { query } from '../../../lib/database';

export const prerender = false;

const { id } = Astro.params;

if (!id) {
  return Astro.redirect('/admin');
}

// Get listing details with all related information
const listingResult = await query(`
  SELECT
    l.*,
    c.name as category_name,
    c.slug as category_slug,
    ci.name as city_name,
    u.email as owner_email
  FROM listings l
  LEFT JOIN categories c ON l.category_primary_id = c.id
  LEFT JOIN cities ci ON l.city_id = ci.id
  LEFT JOIN auth.users u ON l.owner_user_id = u.id
  WHERE l.id = $1
`, [id]);

const listing = listingResult.rows[0];

if (!listing) {
  return Astro.redirect('/admin?error=listing_not_found');
}

// Parse contact info if it exists
let contactInfo: any = {};
try {
  if (listing.contact_info) {
    contactInfo = typeof listing.contact_info === 'string'
      ? JSON.parse(listing.contact_info)
      : listing.contact_info;
  }
} catch (e) {
  console.error('Error parsing contact info:', e);
}

// Parse image gallery if it exists
let imageGallery = [];
try {
  if (listing.image_gallery_paths) {
    imageGallery = Array.isArray(listing.image_gallery_paths) 
      ? listing.image_gallery_paths 
      : JSON.parse(listing.image_gallery_paths);
  }
} catch (e) {
  console.error('Error parsing image gallery:', e);
}

// Format dates
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Get status color
const getStatusColor = (status) => {
  switch (status) {
    case 'active': return 'bg-green-100 text-green-800';
    case 'pending_approval': return 'bg-yellow-100 text-yellow-800';
    case 'rejected': return 'bg-red-100 text-red-800';
    case 'inactive': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};
---

<AdminLayout title={`Review: ${listing.business_name}`}>
  <div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <a href="/admin" class="text-blue-600 hover:text-blue-800 text-sm font-medium mb-2 inline-block">
          ← Back to Admin Dashboard
        </a>
        <h1 class="text-2xl font-bold text-gray-900">{listing.business_name}</h1>
        <div class="flex items-center space-x-3 mt-2">
          <span class={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(listing.listing_status)}`}>
            {listing.listing_status.replace('_', ' ').toUpperCase()}
          </span>
          <span class="text-sm text-gray-600">
            {listing.city_name} • {listing.category_name}
          </span>
          <span class="text-sm text-gray-500">
            Submitted {formatDate(listing.created_at)}
          </span>
        </div>
      </div>
      
      <!-- Action Buttons -->
      {listing.listing_status === 'pending_approval' && (
        <div class="flex space-x-3">
          <button
            onclick={`approveListing('${listing.id}')`}
            class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
          >
            ✓ Approve
          </button>
          <button
            onclick={`showRejectModal('${listing.id}', '${listing.business_name}')`}
            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors"
          >
            ✗ Reject
          </button>
        </div>
      )}
    </div>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Left Column: Business Details -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Business Information</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
              <p class="text-gray-900">{listing.business_name}</p>
            </div>
            
            {listing.display_name && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Display Name</label>
                <p class="text-gray-900">{listing.display_name}</p>
              </div>
            )}
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Location</label>
              <p class="text-gray-900">{listing.city_name}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <p class="text-gray-900">{listing.category_name}</p>
            </div>
            
            {listing.address_full && (
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                <p class="text-gray-900">{listing.address_full}</p>
              </div>
            )}
          </div>
        </div>

        <!-- Descriptions -->
        <div class="bg-white rounded-xl shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Descriptions</h2>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Short Description</label>
              <div class="bg-gray-50 rounded-lg p-3">
                <p class="text-gray-900">{listing.description_short}</p>
              </div>
            </div>
            
            {listing.description_long && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Long Description</label>
                <div class="bg-gray-50 rounded-lg p-3">
                  <p class="text-gray-900 whitespace-pre-wrap">{listing.description_long}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <!-- Business Photos -->
        {(listing.main_image_path || imageGallery.length > 0) && (
          <div class="bg-white rounded-xl shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Business Photos</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {listing.main_image_path && (
                <div class="relative">
                  <img 
                    src={listing.main_image_path} 
                    alt="Main business photo" 
                    class="w-full h-48 object-cover rounded-lg border-2 border-blue-200"
                  />
                  <div class="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                    Main Photo
                  </div>
                </div>
              )}
              
              {imageGallery.map((imageUrl, index) => (
                <div class="relative">
                  <img 
                    src={imageUrl} 
                    alt={`Business photo ${index + 1}`} 
                    class="w-full h-48 object-cover rounded-lg border-2 border-gray-200"
                  />
                  <div class="absolute top-2 left-2 bg-gray-600 text-white text-xs px-2 py-1 rounded">
                    Photo {index + 1}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <!-- Right Column: Contact & Additional Info -->
      <div class="space-y-6">
        <!-- Contact Information -->
        <div class="bg-white rounded-xl shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h2>
          
          <div class="space-y-3">
            {listing.owner_email && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Submitted By</label>
                <p class="text-gray-900">{listing.owner_email}</p>
              </div>
            )}
            
            {contactInfo.phone && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                <p class="text-gray-900">{contactInfo.phone}</p>
              </div>
            )}
            
            {contactInfo.email && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Business Email</label>
                <p class="text-gray-900">{contactInfo.email}</p>
              </div>
            )}
            
            {contactInfo.website && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                <a href={contactInfo.website} target="_blank" class="text-blue-600 hover:text-blue-800">
                  {contactInfo.website}
                </a>
              </div>
            )}
            
            {contactInfo.whatsapp && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">WhatsApp</label>
                <p class="text-gray-900">{contactInfo.whatsapp}</p>
              </div>
            )}
            
            {contactInfo.facebook && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                <a href={contactInfo.facebook} target="_blank" class="text-blue-600 hover:text-blue-800">
                  {contactInfo.facebook}
                </a>
              </div>
            )}
            
            {contactInfo.instagram && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Instagram</label>
                <a href={contactInfo.instagram} target="_blank" class="text-blue-600 hover:text-blue-800">
                  {contactInfo.instagram}
                </a>
              </div>
            )}
          </div>
        </div>

        <!-- Additional Details -->
        <div class="bg-white rounded-xl shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Additional Details</h2>
          
          <div class="space-y-3">
            {listing.languages_spoken && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Languages Spoken</label>
                <p class="text-gray-900">{Array.isArray(listing.languages_spoken) ? listing.languages_spoken.join(', ') : listing.languages_spoken}</p>
              </div>
            )}
            
            {listing.services_offered_keywords && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Services Offered</label>
                <p class="text-gray-900">{Array.isArray(listing.services_offered_keywords) ? listing.services_offered_keywords.join(', ') : listing.services_offered_keywords}</p>
              </div>
            )}
            
            {listing.price_range && (
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Price Range</label>
                <p class="text-gray-900">{listing.price_range}</p>
              </div>
            )}
            
            <div class="grid grid-cols-1 gap-2">
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 mr-2">Owner is Expat:</span>
                <span class={`px-2 py-1 text-xs rounded ${listing.owner_is_expat ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                  {listing.owner_is_expat ? 'Yes' : 'No'}
                </span>
              </div>
              
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 mr-2">Pet Friendly:</span>
                <span class={`px-2 py-1 text-xs rounded ${listing.pet_friendly ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                  {listing.pet_friendly ? 'Yes' : 'No'}
                </span>
              </div>
              
              <div class="flex items-center">
                <span class="text-sm font-medium text-gray-700 mr-2">Kid Friendly:</span>
                <span class={`px-2 py-1 text-xs rounded ${listing.kid_friendly ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                  {listing.kid_friendly ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Submission Details -->
        <div class="bg-white rounded-xl shadow-md p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">Submission Details</h2>
          
          <div class="space-y-3 text-sm">
            <div>
              <span class="font-medium text-gray-700">Submitted:</span>
              <span class="text-gray-900 ml-2">{formatDate(listing.created_at)}</span>
            </div>
            
            {listing.updated_at && listing.updated_at !== listing.created_at && (
              <div>
                <span class="font-medium text-gray-700">Last Updated:</span>
                <span class="text-gray-900 ml-2">{formatDate(listing.updated_at)}</span>
              </div>
            )}
            
            <div>
              <span class="font-medium text-gray-700">Listing ID:</span>
              <span class="text-gray-900 ml-2 font-mono text-xs">{listing.id}</span>
            </div>
            
            <div>
              <span class="font-medium text-gray-700">Slug:</span>
              <span class="text-gray-900 ml-2">{listing.slug}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Reject Modal -->
  <div id="reject-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-xl p-6 max-w-md w-full mx-4">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Reject Business Listing</h3>
      <p class="text-gray-600 mb-4">Please provide a reason for rejecting this business listing:</p>
      
      <textarea
        id="rejection-reason"
        rows="4"
        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
        placeholder="Enter rejection reason (this will be sent to the business owner)..."
      ></textarea>
      
      <div class="flex space-x-3 mt-6">
        <button
          onclick="hideRejectModal()"
          class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onclick="confirmRejectListing()"
          class="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg"
        >
          Reject Listing
        </button>
      </div>
    </div>
  </div>

  <script is:inline>
    let currentListingId = null;
    let currentBusinessName = null;

    // Admin verification
    document.addEventListener('DOMContentLoaded', async () => {
      let attempts = 0;
      while (!window.authFunctions && attempts < 30) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (!window.authFunctions) {
        window.location.href = '/account?error=admin_access_required';
        return;
      }

      const currentUser = await window.authFunctions.getCurrentUser();
      if (!currentUser || currentUser.email !== '<EMAIL>') {
        window.location.href = '/account?error=admin_access_required';
        return;
      }
    });

    async function approveListing(listingId) {
      if (!confirm('Are you sure you want to approve this business listing?')) {
        return;
      }

      try {
        const response = await fetch('/api/admin/approve-listing', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ listingId })
        });

        const result = await response.json();

        if (result.success) {
          alert('✅ Business listing approved successfully!');
          window.location.href = '/admin';
        } else {
          alert('❌ Error approving listing: ' + (result.error || 'Unknown error'));
        }
      } catch (error) {
        console.error('Error:', error);
        alert('❌ Error approving listing');
      }
    }

    function showRejectModal(listingId, businessName) {
      currentListingId = listingId;
      currentBusinessName = businessName;
      document.getElementById('reject-modal').classList.remove('hidden');
      document.getElementById('rejection-reason').focus();
    }

    function hideRejectModal() {
      currentListingId = null;
      currentBusinessName = null;
      document.getElementById('reject-modal').classList.add('hidden');
      document.getElementById('rejection-reason').value = '';
    }

    async function confirmRejectListing() {
      const reason = document.getElementById('rejection-reason').value.trim();
      
      if (!reason) {
        alert('Please provide a reason for rejection.');
        return;
      }

      if (!currentListingId) {
        alert('Error: No listing selected.');
        return;
      }

      try {
        const response = await fetch('/api/admin/reject-listing', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ 
            listingId: currentListingId,
            rejectionReason: reason
          })
        });

        const result = await response.json();

        if (result.success) {
          alert('✅ Business listing rejected successfully!');
          window.location.href = '/admin';
        } else {
          alert('❌ Error rejecting listing: ' + (result.error || 'Unknown error'));
        }
      } catch (error) {
        console.error('Error:', error);
        alert('❌ Error rejecting listing');
      }

      hideRejectModal();
    }

    // Close modal when clicking outside
    document.getElementById('reject-modal').addEventListener('click', function(e) {
      if (e.target === this) {
        hideRejectModal();
      }
    });
  </script>
</AdminLayout>
