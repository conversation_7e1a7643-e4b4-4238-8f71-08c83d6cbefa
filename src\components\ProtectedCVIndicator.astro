---
export interface Props {
  hasCv: boolean;
  className?: string;
}

const { hasCv, className = '' } = Astro.props;
---

{hasCv && (
  <div class={`protected-cv-indicator ${className}`}>
    <!-- CV Indicator for Authenticated Users -->
    <span id="cv-indicator-authenticated" class="hidden px-2 py-1 rounded bg-blue-100 text-blue-700 font-medium">
      📄 CV/Portfolio
    </span>

    <!-- CV Indicator for Non-Authenticated Users (Login Required) -->
    <button
      id="cv-indicator-protected"
      onclick="showSignInModal()"
      class="px-2 py-1 rounded bg-gradient-to-r from-slate-200 to-slate-300 hover:from-slate-300 hover:to-slate-400 text-slate-600 hover:text-slate-700 font-medium transition-all duration-200 cursor-pointer"
      title="Sign in to view CV/Portfolio"
    >
      🔒 CV/Portfolio
    </button>
  </div>
)}

<script is:inline>
  // Initialize CV indicator protection based on auth state
  document.addEventListener('DOMContentLoaded', function() {
    checkCVIndicatorAccess();
    
    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkCVIndicatorAccess);
  });

  function checkCVIndicatorAccess() {
    const currentUser = window.authFunctions?.getCurrentUser();
    const authenticatedIndicators = document.querySelectorAll('#cv-indicator-authenticated');
    const protectedIndicators = document.querySelectorAll('#cv-indicator-protected');
    
    if (currentUser) {
      // User is signed in - show CV indicators
      authenticatedIndicators.forEach(indicator => {
        indicator.classList.remove('hidden');
      });
      protectedIndicators.forEach(indicator => {
        indicator.classList.add('hidden');
      });
    } else {
      // User is not signed in - show protected indicators
      authenticatedIndicators.forEach(indicator => {
        indicator.classList.add('hidden');
      });
      protectedIndicators.forEach(indicator => {
        indicator.classList.remove('hidden');
      });
    }
  }

  // Show sign-in modal (this function should be available globally)
  window.showSignInModal = function() {
    if (window.authFunctions && window.authFunctions.showSignInModal) {
      window.authFunctions.showSignInModal();
    } else {
      // Fallback - redirect to sign-in page
      window.location.href = '/auth/signin?redirect=' + encodeURIComponent(window.location.pathname);
    }
  };
</script>

<style>
  /* Smooth transitions for auth state changes */
  .protected-cv-indicator span,
  .protected-cv-indicator button {
    transition: opacity 0.3s ease-in-out;
  }

  /* Hover effects for the protected indicator */
  .protected-cv-indicator button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
</style>
