import type { APIRoute } from 'astro';
import { insertListing, query } from '../../lib/database';
import { generateUniqueSlug, checkForDuplicates } from '../../lib/utils';

export const POST: APIRoute = async ({ request }) => {
  try {
    const data = await request.json();

    // Check if user has paid for verification (optional)
    let hasVerificationPayment = false;
    if (data.user_id) {
      try {
        const verificationCheck = await query(
          'SELECT id FROM verification_payments WHERE user_id = $1 AND payment_status = $2',
          [data.user_id, 'completed']
        );
        hasVerificationPayment = verificationCheck.rows.length > 0;
      } catch (error) {
        console.log('Verification payment check failed (table may not exist):', error);
      }
    }

    // Validate required fields
    const requiredFields = ['business_name', 'city_id', 'category_primary_id', 'description_short'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return new Response(JSON.stringify({
          error: `Missing required field: ${field}`
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Check for potential duplicates first
    const duplicateCheck = await checkForDuplicates({
      business_name: data.business_name,
      city_id: data.city_id,
      category_primary_id: data.category_primary_id,
      address_full: data.address_full
    });

    if (duplicateCheck.isDuplicate) {
      return new Response(JSON.stringify({
        error: 'Potential duplicate detected',
        duplicate_type: duplicateCheck.type,
        confidence: duplicateCheck.confidence,
        matches: duplicateCheck.matches?.map(match => ({
          id: match.id,
          business_name: match.business_name,
          address_full: match.address_full
        })),
        message: `A similar business may already exist. Please review the matches and confirm this is a different business.`
      }), {
        status: 409, // Conflict
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Generate unique slug within city and subcategory scope with location awareness
    const slug = await generateUniqueSlug(data.business_name, data.city_id, data.category_primary_id, data.address_full);

    // Prepare contact info object
    const contactInfo: any = {};
    if (data.phone) contactInfo.phone = data.phone;
    if (data.email) contactInfo.email = data.email;
    if (data.website_url) contactInfo.website = data.website_url;
    if (data.facebook) contactInfo.facebook = data.facebook;
    if (data.instagram) contactInfo.instagram = data.instagram;
    if (data.twitter) contactInfo.twitter = data.twitter;
    if (data.linkedin) contactInfo.linkedin = data.linkedin;
    if (data.whatsapp) contactInfo.whatsapp = data.whatsapp;
    if (data.youtube) contactInfo.youtube = data.youtube;
    if (data.tiktok) contactInfo.tiktok = data.tiktok;

    // Prepare listing data
    const listingData = {
      slug,
      business_name: data.business_name,
      display_name: data.display_name || null,
      city_id: data.city_id,
      category_primary_id: data.category_primary_id,
      description_short: data.description_short,
      description_long: data.description_long || null,
      contact_info: Object.keys(contactInfo).length > 0 ? contactInfo : null,
      address_full: data.address_full || null,
      languages_spoken: data.languages_spoken || null,
      services_offered_keywords: data.services_offered || null,
      price_range: data.price_range || null,
      owner_is_expat: data.owner_is_expat || false,
      pet_friendly: data.pet_friendly || false,
      kid_friendly: data.kid_friendly || false,
      owner_user_id: data.user_id || null, // Link to user who submitted
      // Handle image uploads
      main_image_path: data.main_image_url || null,
      image_gallery_paths: data.image_urls && data.image_urls.length > 0 ? data.image_urls : null,
      listing_status: 'pending_approval',
      is_verified_expatslist: hasVerificationPayment, // Set to true if they paid for verification
      is_pinned: false,
      view_count: 0,
      click_through_count_website: 0,
      click_through_count_phone: 0,
      click_through_count_facebook: 0,
      click_through_count_instagram: 0,
      click_through_count_twitter: 0,
      click_through_count_linkedin: 0,
      click_through_count_whatsapp: 0,
      click_through_count_youtube: 0,
      click_through_count_tiktok: 0,
      thumbs_up_count: 0,
      thumbs_down_count: 0
    };

    // Insert the listing
    const { data: insertedListing, error } = await insertListing(listingData);

    if (error) {
      console.error('Error inserting listing:', error);
      return new Response(JSON.stringify({
        error: 'Failed to submit business listing'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Create admin notification for new business submission
    try {
      const notificationData = {
        listingId: insertedListing.id,
        businessName: data.business_name,
        hasVerificationPayment,
        userId: data.user_id || null
      };

      await query(`
        INSERT INTO admin_notifications (
          type,
          title,
          message,
          data,
          created_at
        ) VALUES ($1, $2, $3, $4, NOW())
      `, [
        'new_business_submission',
        'New Business Listing Submitted',
        `${data.business_name} has been submitted for approval${hasVerificationPayment ? ' (PAID VERIFICATION)' : ' (FREE LISTING)'}`,
        JSON.stringify(notificationData)
      ]);
    } catch (notificationError) {
      console.log('Admin notification failed (table may not exist):', notificationError);
    }

    return new Response(JSON.stringify({
      success: true,
      listingId: insertedListing.id,
      slug: insertedListing.slug,
      hasVerificationPayment
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
};
