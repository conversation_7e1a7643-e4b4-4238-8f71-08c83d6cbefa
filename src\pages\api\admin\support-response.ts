import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { requestId, response, status } = await request.json();
    
    if (!requestId || !response || !status) {
      return new Response(JSON.stringify({ error: 'Request ID, response, and status are required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Update the support request with admin response
    const result = await query(`
      UPDATE support_requests
      SET
        admin_response = $1::text,
        status = $2::varchar,
        updated_at = NOW(),
        resolved_at = CASE WHEN $2::varchar = 'resolved' THEN NOW() ELSE resolved_at END
      WHERE id = $3::uuid
      RETURNING *
    `, [response, status, requestId]);

    if (result.rows.length === 0) {
      return new Response(JSON.stringify({ error: 'Support request not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({ 
      success: true,
      request: result.rows[0]
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in admin support response API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
