---
// Subcategory listing page: /playa-del-carmen/housing-accommodation/rental-agencies
import Layout from '../../../../layouts/Layout.astro';
import MinimalAcctButton from '../../../../components/MinimalAcctButton.astro';
import ProtectedContact from '../../../../components/ProtectedContact.astro';
import Pagination from '../../../../components/Pagination.astro';
import { getCityBySlug, getCategories, getListingsForSubcategory } from '../../../../lib/database';
import Breadcrumb from '../../../../components/Breadcrumb.astro';

// Set cache headers for better performance
Astro.response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=600'); // 5 min browser, 10 min CDN

const { city: citySlug, category: categorySlug, subcategory: subcategorySlug } = Astro.params;

if (!citySlug || !categorySlug || !subcategorySlug) {
  return Astro.redirect('/cities');
}

// Get city data
const { data: city, error: cityError } = await getCityBySlug(citySlug);

if (cityError || !city) {
  return Astro.redirect('/cities');
}

// Get categories from database
const { data: allCategories, error: categoriesError } = await getCategories();

const mainCategory = allCategories?.find(cat => cat.slug === categorySlug && cat.parent_id === null);
const subcategory = allCategories?.find(cat => cat.slug === subcategorySlug && cat.parent_id === mainCategory?.id);

if (!mainCategory || !subcategory) {
  console.log('Redirecting because mainCategory or subcategory not found');
  return Astro.redirect(`/${citySlug}`);
}

// Icon mapping function for main categories
function getIconForCategory(slug: string): string {
  const iconMap: Record<string, string> = {
    'housing-relocation': '🏠',
    'health-wellness': '🧑‍⚕️',
    'food-dining': '🍽️',
    'professional-legal-financial': '💼',
    'everyday-services-repairs': '🛠️',
    'shopping-leisure-community': '🛍️',
    'education-childcare': '🎓'
  };
  return iconMap[slug] || '📁';
}

// Get category-specific icon for fallbacks
function getCategoryIcon(subcategorySlug: string): string {
  const iconMap: Record<string, string> = {
    // Housing & Relocation
    'rental-agencies': '🏢',
    'real-estate-agents': '🏘️',
    'property-management': '🏠',
    'moving-shipping-companies': '📦',
    'car-rental': '🚗',
    'furniture-stores': '🛋️',

    // Health & Wellness
    'medical-services': '🏥',
    'emergency-medical-services': '🚑',
    'pharmacies': '💊',
    'mental-health-therapy': '🧠',
    'alternative-complementary-medicine': '🌿',
    'fitness-gyms-personal-trainers': '💪',
    'massage-spas-beauty-salons': '💆',
    'yoga-meditation-studios': '🧘',

    // Food & Dining
    'restaurants': '🍽️',
    'cafes-coffee-shops': '☕',
    'grocery-stores-specialty-foods': '🛒',
    'local-food-markets-street-food': '🥬',

    // Professional, Legal & Financial
    'legal-immigration-services': '⚖️',
    'financial-services': '💰',
    'insurance-brokers': '🛡️',
    'business-support': '💼',
    'coworking-spaces': '💻',
    'currency-exchange': '💱',

    // Everyday Services & Repairs
    'connectivity': '📶',
    'home-maintenance-repair': '🔧',
    'appliance-repair': '🔌',
    'clothing-shoe-repair-alterations': '👕',
    'cleaning-services': '🧽',
    'gardeners-pool-maintenance': '🌱',
    'pet-services': '🐕',
    'vehicle-services': '🚗',
    'locksmiths': '🔑',

    // Shopping, Leisure & Community
    'retail-shopping': '🛍️',
    'tour-agencies': '🗺️',
    'bars-nightclubs': '🍻',
    'home-improvement': '🔨',
    'extra-curricular-activities': '🎨',
    'community-social': '👥',

    // Private Education & Childcare
    'schools': '🏫',
    'preschools-daycares': '👶',
    'language-schools': '📚',
    'after-school-programs-tutoring-workshops': '🎓'
  };
  return iconMap[subcategorySlug] || '🏪';
}

const categoryIcon = getCategoryIcon(subcategory.slug);

// Pagination setup
const url = new URL(Astro.request.url);
const currentPage = parseInt(url.searchParams.get('page') || '1');
const itemsPerPage = 10;

// Get listings for this specific subcategory using direct database connection
const { data: allListings, error: listingsError } = await getListingsForSubcategory(city.id, subcategory.id);

// Implement pagination
const totalListings = allListings?.length || 0;
const totalPages = Math.ceil(totalListings / itemsPerPage);
const startIndex = (currentPage - 1) * itemsPerPage;
const endIndex = startIndex + itemsPerPage;
const listings = allListings?.slice(startIndex, endIndex) || [];

const pageTitle = `${subcategory.name} in ${city.name} - ExpatsList`;
const pageDescription = `Find the best ${subcategory.name.toLowerCase()} in ${city.name}. Trusted recommendations from fellow expats.`;
const canonicalUrl = `https://expatslist.org/${citySlug}/${categorySlug}/${subcategorySlug}`;
---

<Layout title={pageTitle} description={pageDescription} canonical={canonicalUrl}>
  <!-- Minimal Account Button -->
  <MinimalAcctButton />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Compact Subcategory Header -->
    <div class="bg-gradient-to-br from-slate-50 via-white to-blue-50 border-b border-slate-200 py-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <!-- Standardized Breadcrumb Navigation -->
        <Breadcrumb
          className="mb-3"
          items={[
            { label: 'Main', href: '/', icon: '🌎' },
            { label: city.name, href: `/${citySlug}`, icon: '📍' },
            { label: mainCategory.name, href: `/${citySlug}/${categorySlug}` },
            { label: subcategory.name, isActive: true }
          ]}
        />

        <!-- Compact Header with Inline Stats -->
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <!-- Left: Category Info -->
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-md">
              <span class="text-xl text-white">{getIconForCategory(mainCategory.slug)}</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-slate-900">{subcategory.name}</h1>
              <p class="text-slate-600 text-sm">
                {totalListings} businesses in {city.name}
                {(() => {
                  const verifiedCount = allListings?.filter(l => l.is_verified_expatslist).length || 0;
                  return verifiedCount > 0 ? ` • ${verifiedCount} verified` : '';
                })()}
              </p>
            </div>
          </div>

          <!-- Right: Quick Stats -->
          <div class="flex items-center space-x-3">
            {(() => {
              const totalViews = allListings?.reduce((sum, listing) => sum + (listing.view_count || 0), 0) || 0;
              const totalClicks = allListings?.reduce((sum, listing) => sum +
                (listing.click_through_count_website || 0) +
                (listing.click_through_count_facebook || 0) +
                (listing.click_through_count_instagram || 0) +
                (listing.click_through_count_twitter || 0) +
                (listing.click_through_count_linkedin || 0) +
                (listing.click_through_count_youtube || 0) +
                (listing.click_through_count_tiktok || 0), 0) || 0;

              return (
                <>
                  {totalViews > 0 && (
                    <div class="bg-white/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/50 shadow-sm">
                      <div class="flex items-center text-sm">
                        <span class="text-green-600 mr-1">👁️</span>
                        <span class="font-bold text-green-600">{totalViews.toLocaleString()}</span>
                        <span class="text-slate-600 ml-1">views</span>
                      </div>
                    </div>
                  )}
                  {totalClicks > 0 && (
                    <div class="bg-white/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/50 shadow-sm">
                      <div class="flex items-center text-sm">
                        <span class="text-blue-600 mr-1">🔗</span>
                        <span class="font-bold text-blue-600">{totalClicks}</span>
                        <span class="text-slate-600 ml-1">clicks</span>
                      </div>
                    </div>
                  )}
                </>
              );
            })()}
          </div>
        </div>
      </div>
    </div>





    <!-- Compact Actions Bar -->
    <div class="bg-white border-b border-slate-200 py-3">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div class="flex items-center space-x-2">
            <a
              href="/add-your-business"
              class="inline-flex items-center px-3 py-2 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
            >
              <span class="mr-1.5">✨</span>
              Add Business
            </a>
            <a
              href={`/${citySlug}/${categorySlug}`}
              class="inline-flex items-center px-3 py-2 bg-slate-100 hover:bg-slate-200 text-slate-700 text-sm font-medium rounded-lg transition-all duration-200"
            >
              <span class="mr-1.5">📂</span>
              All {mainCategory.name}
            </a>
          </div>

          <div class="flex items-center space-x-2 text-sm text-slate-600">
            <span class="font-medium hidden sm:inline">Sort:</span>
            <select class="border border-slate-300 rounded-lg px-3 py-2 text-sm bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option value="popular">Most Popular</option>
              <option value="newest">Newest First</option>
              <option value="verified">Verified First</option>
              <option value="name">Name A-Z</option>
            </select>
          </div>
        </div>
      </div>
    </div>

    <!-- Business Listings Section -->
    <div class="bg-white py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        {listings && listings.length > 0 ? (
          <div>
            <!-- Mobile-Optimized Business Cards Grid -->
            <div class="space-y-3 sm:space-y-4">
            {listings.map((listing, index) => {
              // Use display_name only if it's meaningful (not just first word of business_name)
              const displayName = (listing.display_name && listing.display_name.length > 3 && !listing.business_name.startsWith(listing.display_name + ' '))
                ? listing.display_name
                : listing.business_name;

              return (
              <div class="relative rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group h-auto min-h-[140px] sm:min-h-[160px] business-card-mobile" data-listing-id={listing.id}>
                <!-- Simplified background with better mobile readability -->
                {(listing.main_image_path || listing.google_photo_1) ? (
                  <>
                    <div
                      class="absolute inset-0 bg-cover bg-center transform group-hover:scale-105 transition-transform duration-500"
                      style={`background-image: url('${listing.main_image_path || `/api/image-proxy?url=${encodeURIComponent(listing.google_photo_1)}`}');`}
                    ></div>
                    <!-- Simplified overlay for better mobile readability -->
                    <div class="absolute inset-0 bg-gradient-to-r from-slate-900/80 via-slate-900/70 to-slate-900/80"></div>
                  </>
                ) : (
                  <>
                    <!-- Clean fallback background -->
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-600 to-blue-700"></div>
                  </>
                )}

                <!-- Mobile-optimized content layout -->
                <div class="relative h-full p-4 flex flex-col justify-between min-h-[140px] sm:min-h-[160px]">
                  <!-- Top Section: Business Info -->
                  <div class="flex items-start gap-3 mb-3">
                    <!-- Business Photo or Icon - Mobile Optimized -->
                    <div class="w-16 h-16 sm:w-20 sm:h-20 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg overflow-hidden bg-white/90 backdrop-blur-sm business-photo">
                      {(listing.main_image_path || listing.google_photo_1) ? (
                        <>
                          <img
                            src={listing.main_image_path || `/api/image-proxy?url=${encodeURIComponent(listing.google_photo_1)}`}
                            alt={`${displayName} - Main Photo`}
                            class="w-full h-full object-cover rounded-xl hover:scale-105 transition-transform duration-200"
                            loading="lazy"
                            onerror={`this.style.display='none'; ${!listing.main_image_path && listing.google_photo_2 ? 'this.nextElementSibling.style.display=\'block\';' : 'this.nextElementSibling.nextElementSibling.style.display=\'flex\';'}`}
                          />
                          {!listing.main_image_path && listing.google_photo_2 && (
                            <img
                              src={`/api/image-proxy?url=${encodeURIComponent(listing.google_photo_2)}`}
                              alt={`${displayName} - Second Photo`}
                              class="w-full h-full object-cover rounded-xl hover:scale-105 transition-transform duration-200"
                              loading="lazy"
                              style="display: none;"
                              onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                            />
                          )}
                          <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center" style="display: none;">
                            <span class="text-lg sm:text-xl text-white">{categoryIcon}</span>
                          </div>
                        </>
                      ) : (
                        <div class="w-full h-full bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                          <span class="text-lg sm:text-xl text-white">{categoryIcon}</span>
                        </div>
                      )}
                    </div>

                    <!-- Business Name and Info -->
                    <div class="flex-1 min-w-0">
                      <div class="flex items-start justify-between">
                        <div class="flex-1 min-w-0">
                          <h3 class="text-lg sm:text-xl font-bold text-white leading-tight mb-1">
                            <a href={`/${citySlug}/${categorySlug}/${subcategorySlug}/${listing.slug}`} class="hover:text-blue-200 transition-colors">
                              {displayName}
                            </a>
                          </h3>
                          <p class="text-sm text-blue-100 font-medium">
                            {subcategory.name}
                          </p>
                        </div>

                        <!-- Key Badges Only -->
                        <div class="flex flex-col gap-1 ml-2">
                          {listing.is_verified_expatslist && (
                            <span class="px-2 py-1 bg-blue-500 text-white text-xs font-bold rounded-full shadow-md">
                              ✓
                            </span>
                          )}
                          {(subcategory.slug.includes('rental') || subcategory.slug.includes('hotel') || subcategory.slug.includes('restaurant') || subcategory.slug.includes('spa') || subcategory.slug.includes('massage') || subcategory.slug.includes('dining')) && listing.price_range && (
                            <span class="px-2 py-1 bg-emerald-500 text-white text-xs font-bold rounded-full shadow-md">
                              {listing.price_range === '$' ? '$' : listing.price_range === '$$' ? '$$' : listing.price_range === '$$$' ? '$$$' : listing.price_range}
                            </span>
                          )}
                        </div>
                      </div>



                      <!-- Key Features Row - Mobile Optimized -->
                      <div class="flex flex-wrap items-center gap-1.5 mb-2 feature-pills">
                        {/* Key Feature Pills - Most Important Only */}
                        {listing.pet_friendly && (
                          <span class="px-2 py-1 bg-white/90 text-amber-700 text-xs font-semibold rounded-full shadow-sm">
                            🐕 Pet OK
                          </span>
                        )}
                        {listing.kid_friendly && (
                          <span class="px-2 py-1 bg-white/90 text-pink-700 text-xs font-semibold rounded-full shadow-sm">
                            👶 Family
                          </span>
                        )}
                        {listing.owner_is_expat && (
                          <span class="px-2 py-1 bg-white/90 text-purple-700 text-xs font-semibold rounded-full shadow-sm">
                            🌍 Expat
                          </span>
                        )}
                        {listing.languages_spoken && listing.languages_spoken.includes('English') && (
                          <span class="px-2 py-1 bg-white/90 text-blue-700 text-xs font-semibold rounded-full shadow-sm">
                            🇺🇸 English
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <!-- Bottom Section: Contact & Action -->
                  <div class="flex items-center justify-between gap-3">
                    <!-- Contact Methods - Mobile Optimized -->
                    <div class="flex items-center gap-2 contact-pills">
                      {/* Admin Delete Button (hidden by default) */}
                      <button
                        class="admin-delete-business-btn hidden px-2 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors"
                        data-business-id={listing.id}
                        data-business-name={displayName}
                        onclick="deleteBusinessListing(this)"
                      >
                        Delete
                      </button>

                      {/* Mobile-Optimized Contact Pills */}
                      <ProtectedContact
                        phone={listing.contact_info?.phone}
                        email={listing.contact_info?.email}
                        whatsapp={listing.contact_info?.whatsapp}
                        type="business"
                        size="small"
                        style="pill"
                      />

                      {/* Website Icon */}
                      {listing.contact_info?.website && listing.contact_info.website.trim() !== '' && (
                        <span class="bg-white/90 text-green-700 px-2 py-1.5 rounded-full text-xs font-semibold shadow-sm">
                          🌐
                        </span>
                      )}
                    </div>

                    <!-- Mobile-Optimized CTA Button -->
                    <a
                      href={`/${citySlug}/${categorySlug}/${subcategorySlug}/${listing.slug}`}
                      class="group px-4 py-2.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-sm font-bold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl min-h-[44px] flex items-center justify-center transform hover:-translate-y-0.5 min-w-[80px]"
                    >
                      <span class="group-hover:scale-105 transition-transform inline-block">View Details</span>
                    </a>
                  </div>

                  <!-- Engagement Stats - Positioned to avoid overlap -->
                  {(() => {
                    const totalEngagement = (listing.view_count || 0) +
                      (listing.click_through_count_website || 0) +
                      (listing.click_through_count_facebook || 0) +
                      (listing.click_through_count_instagram || 0) +
                      (listing.click_through_count_twitter || 0) +
                      (listing.click_through_count_youtube || 0);

                    return totalEngagement > 10 && (
                      <div class="absolute top-2 left-2 bg-white/95 backdrop-blur-sm rounded-lg px-2 py-1 shadow-sm z-10">
                        <div class="flex items-center gap-1 text-xs font-medium">
                          {listing.view_count > 0 && (
                            <div class="flex items-center text-blue-600">
                              <span class="mr-1">👁️</span>
                              <span class="font-bold">{listing.view_count}</span>
                            </div>
                          )}
                          {totalEngagement > 50 && (
                            <span class="text-yellow-600">🔥</span>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              </div>
            );
            })}
          </div>

          <!-- Enhanced Results Summary with Community Insights -->
          <div class="mt-12">
            <div class="bg-gradient-to-br from-white via-blue-50 to-indigo-50 rounded-2xl shadow-lg border border-blue-200/50 p-8 mb-8">
              <div class="text-center mb-6">
                <h3 class="text-2xl font-bold text-slate-900 mb-2">Community Insights</h3>
                <p class="text-lg text-slate-700 font-medium">
                  Showing <span class="font-bold text-blue-600">{startIndex + 1}-{Math.min(endIndex, totalListings)}</span> of <span class="font-bold text-blue-600">{totalListings}</span> {subcategory.name.toLowerCase()} businesses in <span class="font-bold text-slate-900">{city.name}</span>
                </p>
              </div>

              {/* Community Engagement Stats - Real-time data */}
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                {(() => {
                  const totalViews = allListings?.reduce((sum, listing) => sum + (listing.view_count || 0), 0) || 0;
                  const totalClicks = allListings?.reduce((sum, listing) => sum +
                    (listing.click_through_count_website || 0) +
                    (listing.click_through_count_facebook || 0) +
                    (listing.click_through_count_instagram || 0) +
                    (listing.click_through_count_twitter || 0) +
                    (listing.click_through_count_linkedin || 0) +
                    (listing.click_through_count_youtube || 0) +
                    (listing.click_through_count_tiktok || 0), 0) || 0;
                  const verifiedCount = allListings?.filter(l => l.is_verified_expatslist).length || 0;

                  return (
                    <>
                      <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 text-center border border-white/50">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                          <span class="text-blue-600 text-xl">👁️</span>
                        </div>
                        <p class="text-2xl font-bold text-blue-600">{totalViews.toLocaleString()}</p>
                        <p class="text-sm text-slate-600 font-medium">Total Views</p>
                        <p class="text-xs text-slate-500">Community engagement</p>
                      </div>

                      <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 text-center border border-white/50">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                          <span class="text-green-600 text-xl">🔗</span>
                        </div>
                        <p class="text-2xl font-bold text-green-600">{totalClicks.toLocaleString()}</p>
                        <p class="text-sm text-slate-600 font-medium">Link Clicks</p>
                        <p class="text-xs text-slate-500">Business connections</p>
                      </div>

                      <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 text-center border border-white/50">
                        <div class="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-2">
                          <span class="text-emerald-600 text-xl">✓</span>
                        </div>
                        <p class="text-2xl font-bold text-emerald-600">{verifiedCount}</p>
                        <p class="text-sm text-slate-600 font-medium">Verified</p>
                        <p class="text-xs text-slate-500">Quality assured</p>
                      </div>
                    </>
                  );
                })()}
              </div>

              {/* Trust Indicators */}
              <div class="mt-6 text-center">
                <p class="text-sm text-slate-600 leading-relaxed">
                  <span class="font-semibold text-slate-800">🛡️ Quality Guaranteed:</span> All businesses are verified by our expat community.
                  <span class="font-semibold text-slate-800">📊 Real Engagement:</span> View counts and clicks represent genuine community interest.
                </p>
              </div>
            </div>

            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              baseUrl={`/${citySlug}/${categorySlug}/${subcategorySlug}`}
              totalItems={totalListings}
              itemsPerPage={itemsPerPage}
              itemName={`${subcategory.name.toLowerCase()} businesses in ${city.name}`}
            />
          </div>
        </div>
      ) : (
        <!-- Premium Empty State -->
        <div class="text-center py-20">
          <div class="bg-white rounded-3xl shadow-2xl border border-slate-200/50 p-12 max-w-2xl mx-auto">
            <div class="w-32 h-32 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-8">
              <span class="text-6xl">{getIconForCategory(mainCategory.slug)}</span>
            </div>
            <h2 class="text-4xl font-bold text-slate-900 mb-6">No businesses yet</h2>
            <p class="text-xl text-slate-600 mb-10 leading-relaxed">
              Be the first to add a {subcategory.name.toLowerCase()} business in {city.name} and help fellow expats discover great local services.
            </p>
            <a
              href="/add-your-business"
              class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold rounded-2xl transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
            >
              <span class="mr-3 text-xl">✨</span>
              Add Your Business
            </a>
          </div>
        </div>
      )}
    </div>

    <!-- Premium Back Navigation -->
    <div class="max-w-7xl mx-auto px-6 pb-12">
      <div class="bg-white rounded-2xl shadow-lg border border-slate-200/50 p-8">
        <div class="flex justify-center space-x-6">
          <a
            href={`/${citySlug}/${categorySlug}`}
            class="group px-8 py-4 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <span class="group-hover:scale-105 transition-transform inline-block">← Back to {mainCategory.name}</span>
          </a>
          <a
            href={`/${citySlug}`}
            class="group px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            <span class="group-hover:scale-105 transition-transform inline-block">All Categories</span>
          </a>
        </div>
      </div>
    </div>
  </main>
</Layout>

<script is:inline define:vars={{ citySlug, categorySlug, subcategorySlug: subcategory.slug }}>
  // Check if user is admin and show admin controls
  document.addEventListener('DOMContentLoaded', () => {
    function checkAdminStatus() {
      const currentUser = window.authFunctions?.getCurrentUser();
      const userProfile = window.authFunctions?.getUserProfile();

      console.log('Business Subcategory Admin check - User:', currentUser?.email, 'Profile:', userProfile?.role);

      if (currentUser && (userProfile?.role === 'administrator' || currentUser.email === '<EMAIL>')) {
        console.log('Admin detected, showing business delete buttons');
        // Show all admin delete buttons
        const deleteButtons = document.querySelectorAll('.admin-delete-business-btn');
        deleteButtons.forEach(btn => {
          btn.classList.remove('hidden');
        });
      }
    }

    // Try immediately
    checkAdminStatus();

    // Also try after a delay in case auth is still loading
    setTimeout(checkAdminStatus, 1000);

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAdminStatus);
  });

  // Global function to delete business listings
  window.deleteBusinessListing = async function(button) {
    const businessId = button.getAttribute('data-business-id');
    const businessName = button.getAttribute('data-business-name');

    const currentUser = window.authFunctions?.getCurrentUser();
    const userProfile = window.authFunctions?.getUserProfile();

    console.log('Delete attempt - User:', currentUser?.email, 'Profile:', userProfile?.role);

    if (!currentUser || (userProfile?.role !== 'administrator' && currentUser.email !== '<EMAIL>')) {
      // Show unauthorized state on button
      button.innerHTML = '❌ Unauthorized';
      button.classList.add('bg-red-800');
      setTimeout(() => {
        button.innerHTML = 'Delete';
        button.classList.remove('bg-red-800');
      }, 2000);
      return;
    }

    try {
      // Update button to show loading state
      button.disabled = true;
      button.innerHTML = '<span class="animate-spin">⏳</span> Deleting...';
      button.classList.add('opacity-75');

      const response = await fetch('/api/admin/delete-business', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          businessId: businessId,
          adminUserId: currentUser.id
        })
      });

      const result = await response.json();

      if (response.ok) {
        // Show success state briefly
        button.innerHTML = '✅ Deleted';
        button.classList.remove('bg-red-600', 'hover:bg-red-700');
        button.classList.add('bg-green-600');

        // Animate removal after brief success feedback
        setTimeout(() => {
          const businessElement = button.closest('.relative');
          if (businessElement) {
            businessElement.style.transition = 'all 0.3s ease-out';
            businessElement.style.transform = 'translateX(-100%)';
            businessElement.style.opacity = '0';
            setTimeout(() => businessElement.remove(), 300);
          }
        }, 1000);
      } else {
        // Show error state
        button.innerHTML = '❌ Failed';
        button.classList.remove('bg-red-600');
        button.classList.add('bg-red-800');

        // Reset after 2 seconds
        setTimeout(() => {
          button.disabled = false;
          button.innerHTML = 'Delete';
          button.classList.remove('bg-red-800', 'opacity-75');
          button.classList.add('bg-red-600', 'hover:bg-red-700');
        }, 2000);
      }
    } catch (error) {
      console.error('Error deleting business:', error);

      // Show error state
      button.innerHTML = '❌ Error';
      button.classList.remove('bg-red-600');
      button.classList.add('bg-red-800');

      // Reset after 2 seconds
      setTimeout(() => {
        button.disabled = false;
        button.innerHTML = 'Delete';
        button.classList.remove('bg-red-800', 'opacity-75');
        button.classList.add('bg-red-600', 'hover:bg-red-700');
      }, 2000);
    }
  };
</script>

<script>
  // Track views for all listings displayed on the page
  document.addEventListener('DOMContentLoaded', function() {
    const listingCards = document.querySelectorAll('[data-listing-id]');
    const trackedViews = new Set();

    // Use Intersection Observer to track when listings come into view
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
          const listingId = entry.target.getAttribute('data-listing-id');

          // Only track each listing once per page load
          if (listingId && !trackedViews.has(listingId)) {
            trackedViews.add(listingId);

            // Track the view
            fetch('/api/track-click', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                listingId: listingId,
                type: 'view'
              })
            }).catch(() => {
              // Silently fail - don't interrupt user experience
            });
          }
        }
      });
    }, {
      threshold: 0.5, // Trigger when 50% of the listing is visible
      rootMargin: '0px 0px -50px 0px' // Add some margin to ensure user actually sees it
    });

    // Observe all listing cards
    listingCards.forEach(card => {
      observer.observe(card);
    });
  });

  // No additional JavaScript needed for price display
</script>
