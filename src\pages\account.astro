---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';

const pageTitle = 'My Account';
const pageDescription = 'Manage your ExpatsList account, posts, and business listings.';
---

<Layout title={pageTitle} description={pageDescription}>
  <Navigation currentPath="/account" />

  <main class="min-h-screen bg-slate-50 pt-16 sm:pt-12">
    <!-- Header -->
    <div class="bg-white border-b border-slate-200 py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-blue-600 rounded-xl flex items-center justify-center">
              <span class="text-2xl">👤</span>
            </div>
            <div>
              <h1 class="text-2xl font-bold text-slate-900">My Account</h1>
              <p class="text-slate-600 text-sm">Manage your profile, posts, and business listings</p>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- Account Required Notice -->
    <div id="account-auth-required" class="py-16">
      <div class="max-w-2xl mx-auto px-6 text-center">
        <div class="bg-emerald-50 border border-emerald-200 rounded-xl p-8">
          <div class="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-emerald-600 text-2xl">🔐</span>
          </div>
          <h3 class="text-xl font-semibold text-emerald-900 mb-2">Sign In Required</h3>
          <p class="text-emerald-700 mb-6">Please sign in to access your account dashboard</p>
          <button
            id="account-signin-btn"
            class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Sign In to Continue
          </button>
        </div>
      </div>
    </div>

    <!-- Account Content -->
    <div id="account-content" class="hidden py-6">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">

        <!-- Tab Navigation -->
        <div class="bg-white rounded-t-xl border border-slate-200 border-b-0">
          <div class="flex overflow-x-auto">
            <button class="tab-btn active flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 border-emerald-500 text-emerald-600 whitespace-nowrap" data-tab="profile">
              <span>👤</span>
              <span>Profile</span>
            </button>
            <button class="tab-btn flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 border-transparent text-slate-500 hover:text-slate-700 whitespace-nowrap" data-tab="classifieds">
              <span>📝</span>
              <span>My Classifieds</span>
            </button>
            <button class="tab-btn flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 border-transparent text-slate-500 hover:text-slate-700 whitespace-nowrap" data-tab="business">
              <span>🏢</span>
              <span>Business</span>
            </button>
            <button class="tab-btn flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 border-transparent text-slate-500 hover:text-slate-700 whitespace-nowrap" data-tab="security">
              <span>🔒</span>
              <span>Security</span>
            </button>
            <button class="tab-btn flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 border-transparent text-slate-500 hover:text-slate-700 whitespace-nowrap" data-tab="support">
              <span>💬</span>
              <span>Support</span>
            </button>

            <!-- Admin Panel Link (only visible to admin users) -->
            <a id="admin-panel-link" href="/admin" class="hidden flex items-center space-x-2 px-6 py-4 text-sm font-medium border-b-2 border-transparent text-red-600 hover:text-red-700 hover:border-red-200 hover:bg-red-50 whitespace-nowrap transition-all duration-200">
              <span class="text-base">⚡</span>
              <span class="font-semibold">Admin Panel</span>
            </a>
          </div>
        </div>

        <!-- Tab Content Container -->
        <div class="bg-white rounded-b-xl border border-slate-200 border-t-0">

          <!-- Profile Tab -->
          <div id="profile-tab" class="tab-content p-6">
            <div class="max-w-2xl">
              <!-- Welcome Message -->
              <div id="user-welcome" class="hidden bg-gradient-to-r from-emerald-50 to-blue-50 border border-emerald-200 rounded-xl p-4 mb-6">
                <div class="flex items-center space-x-3">
                  <div class="w-10 h-10 bg-emerald-100 rounded-full flex items-center justify-center">
                    <span class="text-emerald-600 text-lg">👋</span>
                  </div>
                  <div>
                    <h3 class="font-semibold text-emerald-900">
                      Welcome back, <span id="user-display-name">User</span>!
                    </h3>
                    <p class="text-sm text-emerald-700">Manage your ExpatsList profile and settings</p>
                  </div>
                </div>
              </div>

              <h2 class="text-xl font-bold text-slate-900 mb-6">Profile Information</h2>

              <form id="profile-form" class="space-y-6">
                <!-- Display Name -->
                <div>
                  <label for="display-name" class="block text-sm font-medium text-slate-700 mb-2">
                    Community Display Name
                  </label>
                  <input
                    type="text"
                    id="display-name"
                    name="display_name"
                    placeholder="How you'd like to be known in the community"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  />
                  <p class="text-xs text-slate-500 mt-1">This name will be shown on your classified posts and community interactions</p>
                </div>

                <!-- Email (Read-only) -->
                <div>
                  <label for="email" class="block text-sm font-medium text-slate-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    readonly
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg bg-slate-50 text-slate-600 cursor-not-allowed"
                  />
                  <p class="text-xs text-slate-500 mt-1">Email cannot be changed. Contact support if you need to update this.</p>
                </div>

                <!-- Bio/About -->
                <div>
                  <label for="bio" class="block text-sm font-medium text-slate-700 mb-2">
                    About You (Optional)
                  </label>
                  <textarea
                    id="bio"
                    name="bio"
                    rows="3"
                    maxlength="500"
                    placeholder="Tell the community a bit about yourself..."
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors resize-none"
                  ></textarea>
                  <div class="flex justify-between text-xs text-slate-500 mt-1">
                    <span>Optional bio for your profile</span>
                    <span><span id="bio-count">0</span>/500</span>
                  </div>
                </div>

                <!-- Location -->
                <div>
                  <label for="location" class="block text-sm font-medium text-slate-700 mb-2">
                    Current Location (Optional)
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    placeholder="e.g., Playa del Carmen, Mexico"
                    class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                  />
                  <p class="text-xs text-slate-500 mt-1">Help other expats know where you're based</p>
                </div>

                <!-- Save Button -->
                <div class="flex justify-end pt-4">
                  <button
                    type="submit"
                    id="save-profile-btn"
                    class="bg-emerald-600 hover:bg-emerald-700 disabled:bg-slate-400 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
                  >
                    <span id="save-profile-text">Save Changes</span>
                    <div id="save-profile-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </button>
                </div>
              </form>
            </div>
          </div>

          <!-- Classifieds Tab -->
          <div id="classifieds-tab" class="tab-content hidden p-6">
            <div class="mb-6">
              <h2 class="text-xl font-bold text-slate-900">My Classified Posts</h2>
              <p class="text-slate-600 text-sm mt-1">Click on any post to view details, or use the action buttons to manage your listings</p>
            </div>

            <!-- Classifieds Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div class="bg-slate-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-emerald-600 mb-1" id="total-posts">0</div>
                <div class="text-xs text-slate-600">Total Posts</div>
              </div>
              <div class="bg-slate-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600 mb-1" id="active-posts">0</div>
                <div class="text-xs text-slate-600">Active</div>
              </div>
              <div class="bg-slate-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-yellow-600 mb-1" id="hidden-posts">0</div>
                <div class="text-xs text-slate-600">Hidden</div>
              </div>
              <div class="bg-slate-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-slate-600 mb-1" id="total-views">0</div>
                <div class="text-xs text-slate-600">Total Views</div>
              </div>
            </div>

            <!-- Posts List -->
            <div id="user-posts-list" class="space-y-4">
              <div class="text-center py-8 text-slate-500">
                <span class="text-4xl mb-4 block">📝</span>
                <p>Loading your classified posts...</p>
              </div>
            </div>
          </div>

          <!-- Business Tab -->
          <div id="business-tab" class="tab-content hidden p-6">
            <h2 class="text-xl font-bold text-slate-900 mb-4">Business Management</h2>

            <!-- Coming Soon Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div class="flex items-center space-x-2">
                <span class="text-blue-600 text-lg">🚀</span>
                <p class="text-blue-800 font-medium">These features are coming soon!</p>
              </div>
              <p class="text-blue-700 text-sm mt-1">Business claiming and verification features will be available in the next update.</p>
            </div>

            <!-- Business Actions Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <!-- Claim Business -->
              <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
                <div class="flex items-center space-x-3 mb-4">
                  <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span class="text-blue-600 text-xl">🏢</span>
                  </div>
                  <div>
                    <h3 class="font-semibold text-blue-900">Claim Your Business</h3>
                    <p class="text-sm text-blue-700">Take control of your business listing</p>
                  </div>
                </div>
                <button id="claim-business-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                  Search & Claim Business
                </button>
              </div>

              <!-- Verify Business -->
              <div class="bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200 rounded-xl p-6">
                <div class="flex items-center space-x-3 mb-4">
                  <div class="w-10 h-10 bg-emerald-100 rounded-lg flex items-center justify-center">
                    <span class="text-emerald-600 text-xl">✅</span>
                  </div>
                  <div>
                    <h3 class="font-semibold text-emerald-900">Verify Your Business</h3>
                    <p class="text-sm text-emerald-700">Get a verified checkmark</p>
                  </div>
                </div>
                <button id="verify-business-btn" class="w-full bg-emerald-600 hover:bg-emerald-700 text-white px-4 py-3 rounded-lg font-medium transition-colors">
                  Start Verification
                </button>
              </div>
            </div>

            <!-- My Business Listings -->
            <div class="bg-white border border-slate-200 rounded-xl p-6">
              <h3 class="text-lg font-semibold text-slate-900 mb-4">My Business Listings</h3>
              <div id="user-business-list" class="space-y-4">
                <div class="text-center py-8 text-slate-500">
                  <span class="text-4xl mb-4 block">🏢</span>
                  <p>No business listings found</p>
                  <p class="text-sm mt-2">Claim or verify your business to manage it here</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Security Tab -->
          <div id="security-tab" class="tab-content hidden p-6">
            <div class="max-w-2xl">
              <h2 class="text-xl font-bold text-slate-900 mb-6">Security Settings</h2>

              <!-- Change Password -->
              <div class="bg-white border border-slate-200 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-slate-900 mb-4">Change Password</h3>

                <!-- Password Requirements -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h4 class="text-sm font-semibold text-blue-900 mb-2">Password Requirements:</h4>
                  <ul class="text-sm text-blue-800 space-y-1">
                    <li class="flex items-center space-x-2">
                      <span class="w-1.5 h-1.5 bg-blue-600 rounded-full"></span>
                      <span>At least 8 characters long</span>
                    </li>
                    <li class="flex items-center space-x-2">
                      <span class="w-1.5 h-1.5 bg-blue-600 rounded-full"></span>
                      <span>Mix of uppercase and lowercase letters recommended</span>
                    </li>
                    <li class="flex items-center space-x-2">
                      <span class="w-1.5 h-1.5 bg-blue-600 rounded-full"></span>
                      <span>Include numbers and special characters for better security</span>
                    </li>
                  </ul>
                </div>

                <form id="password-form" class="space-y-4">
                  <div>
                    <label for="current-password" class="block text-sm font-medium text-slate-700 mb-2">
                      Current Password
                    </label>
                    <input
                      type="password"
                      id="current-password"
                      name="current_password"
                      required
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="Enter your current password"
                    />
                  </div>
                  <div>
                    <label for="new-password" class="block text-sm font-medium text-slate-700 mb-2">
                      New Password
                    </label>
                    <input
                      type="password"
                      id="new-password"
                      name="new_password"
                      required
                      minlength="8"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="Enter your new password (min. 8 characters)"
                    />
                    <div id="password-strength" class="mt-2 hidden">
                      <div class="flex items-center space-x-2">
                        <div class="flex-1 bg-gray-200 rounded-full h-2">
                          <div id="strength-bar" class="h-2 rounded-full transition-all duration-300"></div>
                        </div>
                        <span id="strength-text" class="text-xs font-medium"></span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label for="confirm-password" class="block text-sm font-medium text-slate-700 mb-2">
                      Confirm New Password
                    </label>
                    <input
                      type="password"
                      id="confirm-password"
                      name="confirm_password"
                      required
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                      placeholder="Confirm your new password"
                    />
                    <div id="password-match" class="mt-1 text-xs hidden">
                      <span id="match-text"></span>
                    </div>
                  </div>
                  <button
                    type="submit"
                    id="change-password-btn"
                    class="bg-emerald-600 hover:bg-emerald-700 disabled:bg-slate-400 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
                  >
                    <span id="change-password-text">Update Password</span>
                    <div id="change-password-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </button>
                </form>
              </div>

              <!-- Account Management -->
              <div class="bg-red-50 border border-red-200 rounded-xl p-6">
                <h3 class="text-lg font-semibold text-red-900 mb-4">Danger Zone</h3>
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="text-sm font-medium text-red-900">Delete Account</h4>
                    <p class="text-xs text-red-700 mt-1">Permanently delete your account and all data</p>
                  </div>
                  <button
                    id="delete-account-btn"
                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                  >
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- Support Tab -->
          <div id="support-tab" class="tab-content hidden p-6">
            <div class="max-w-4xl">
              <h2 class="text-xl font-bold text-slate-900 mb-6">Support & Feedback</h2>

              <!-- Support History -->
              <div class="bg-white border border-slate-200 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-slate-900 mb-4">Your Support Requests</h3>
                <div id="support-history-list" class="space-y-4">
                  <div class="text-center py-8 text-slate-500">
                    <span class="text-4xl mb-4 block">📧</span>
                    <p>Loading your support requests...</p>
                  </div>
                </div>
              </div>

              <!-- Contact Support -->
              <div class="bg-white border border-slate-200 rounded-xl p-6 mb-6">
                <h3 class="text-lg font-semibold text-slate-900 mb-4">Submit New Request</h3>
                <form id="support-form" class="space-y-4">
                  <div>
                    <label for="support-type" class="block text-sm font-medium text-slate-700 mb-2">
                      Type of Request
                    </label>
                    <select
                      id="support-type"
                      name="support_type"
                      required
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    >
                      <option value="">Select request type</option>
                      <option value="bug_report">🐛 Bug Report</option>
                      <option value="feature_request">💡 Feature Request</option>
                      <option value="business_correction">🏢 Business Listing Correction</option>
                      <option value="account_help">👤 Account Help</option>
                      <option value="general_inquiry">💬 General Inquiry</option>
                      <option value="other">❓ Other</option>
                    </select>
                  </div>
                  <div>
                    <label for="support-subject" class="block text-sm font-medium text-slate-700 mb-2">
                      Subject
                    </label>
                    <input
                      type="text"
                      id="support-subject"
                      name="subject"
                      required
                      placeholder="Brief description of your request"
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors"
                    />
                  </div>
                  <div>
                    <label for="support-message" class="block text-sm font-medium text-slate-700 mb-2">
                      Message
                    </label>
                    <textarea
                      id="support-message"
                      name="message"
                      required
                      rows="5"
                      placeholder="Please provide details about your request..."
                      class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors resize-none"
                    ></textarea>
                  </div>
                  <button
                    type="submit"
                    id="submit-support-btn"
                    class="bg-emerald-600 hover:bg-emerald-700 disabled:bg-slate-400 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center"
                  >
                    <span id="submit-support-text">Send Message</span>
                    <div id="submit-support-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </button>
                </form>
              </div>

              <!-- Quick Help -->
              <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">Quick Help</h3>
                <div class="space-y-3">
                  <a href="/help/posting-classifieds" class="flex items-center space-x-3 text-blue-700 hover:text-blue-800 transition-colors">
                    <span>📝</span>
                    <span class="text-sm">How to post classified ads</span>
                  </a>
                  <a href="/help/business-listings" class="flex items-center space-x-3 text-blue-700 hover:text-blue-800 transition-colors">
                    <span>🏢</span>
                    <span class="text-sm">Managing business listings</span>
                  </a>
                  <a href="/help/account-settings" class="flex items-center space-x-3 text-blue-700 hover:text-blue-800 transition-colors">
                    <span>⚙️</span>
                    <span class="text-sm">Account settings guide</span>
                  </a>
                </div>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="success-message" class="hidden fixed top-4 right-4 bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-lg z-50">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="text-emerald-700 font-medium" id="success-text">Success!</span>
      </div>
    </div>

    <div id="error-message" class="hidden fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 shadow-lg z-50">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        <span class="text-red-700 font-medium" id="error-text">Error occurred</span>
      </div>
    </div>
  </main>

  <!-- Business Rejection Appeal Modal -->
  <div id="appeal-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Modal Header -->
      <div class="p-6 border-b border-slate-200">
        <div class="flex items-center justify-between">
          <h3 class="text-xl font-semibold text-slate-900">Appeal Business Rejection</h3>
          <button onclick="hideAppealModal()" class="text-slate-400 hover:text-slate-600 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Content -->
      <div class="p-6">
        <!-- Business Info -->
        <div class="bg-slate-50 rounded-lg p-4 mb-6">
          <h4 class="font-medium text-slate-900 mb-2">Business: <span id="appeal-business-name" class="text-blue-600"></span></h4>
          <div class="bg-red-50 border border-red-200 rounded-lg p-3">
            <h5 class="font-medium text-red-900 mb-1">Rejection Reason:</h5>
            <p id="appeal-rejection-reason" class="text-sm text-red-800"></p>
          </div>
        </div>

        <!-- Appeal Form -->
        <form id="appeal-form" class="space-y-4">
          <input type="hidden" id="appeal-listing-id" />

          <div>
            <label for="appeal-subject" class="block text-sm font-medium text-slate-700 mb-2">
              Subject
            </label>
            <input
              type="text"
              id="appeal-subject"
              name="subject"
              value="Appeal for Business Listing Rejection"
              required
              class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            />
          </div>

          <div>
            <label for="appeal-message" class="block text-sm font-medium text-slate-700 mb-2">
              Your Message to Admin
            </label>
            <textarea
              id="appeal-message"
              name="message"
              rows="6"
              required
              placeholder="Please explain why you believe your business listing should be reconsidered. Provide any additional information or clarifications that might help resolve the rejection reason..."
              class="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
            ></textarea>
            <p class="text-xs text-slate-500 mt-1">Be specific and professional. This message will be sent directly to our admin team.</p>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-3 pt-4">
            <button
              type="button"
              onclick="hideAppealModal()"
              class="flex-1 px-4 py-3 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              id="submit-appeal-btn"
              class="flex-1 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center"
            >
              <span id="submit-appeal-text">Send Appeal</span>
              <div id="submit-appeal-spinner" class="hidden ml-2 w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </button>
          </div>
        </form>

        <!-- Help Text -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span class="text-blue-600 text-sm">💡</span>
            </div>
            <div>
              <h5 class="font-medium text-blue-900 mb-1">Tips for a Successful Appeal</h5>
              <ul class="text-sm text-blue-800 space-y-1">
                <li>• Address the specific rejection reason mentioned above</li>
                <li>• Provide accurate and complete business information</li>
                <li>• Be professional and courteous in your communication</li>
                <li>• Include any supporting documentation if relevant</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Account JavaScript -->
  <script is:inline>
    // Global variables
    let currentUser = null;
    let userProfile = null;

    // DOM elements
    const authRequired = document.getElementById('account-auth-required');
    const accountContent = document.getElementById('account-content');
    const profileForm = document.getElementById('profile-form');
    const successMessage = document.getElementById('success-message');
    const errorMessage = document.getElementById('error-message');

    // Tab functionality
    function initializeTabs() {
      const tabButtons = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');

      tabButtons.forEach(button => {
        button.addEventListener('click', () => {
          const targetTab = button.getAttribute('data-tab');
          switchToTab(targetTab);
        });
      });
    }

    // Standalone function to switch to a specific tab
    function switchToTab(targetTab) {
      console.log('Switching to tab:', targetTab);

      const tabButtons = document.querySelectorAll('.tab-btn');
      const tabContents = document.querySelectorAll('.tab-content');

      // Update button states
      tabButtons.forEach(btn => {
        const btnTab = btn.getAttribute('data-tab');
        if (btnTab === targetTab) {
          btn.classList.add('active', 'border-emerald-500', 'text-emerald-600');
          btn.classList.remove('border-transparent', 'text-slate-500');
        } else {
          btn.classList.remove('active', 'border-emerald-500', 'text-emerald-600');
          btn.classList.add('border-transparent', 'text-slate-500');
        }
      });

      // Update content visibility
      tabContents.forEach(content => {
        content.classList.add('hidden');
      });

      const targetTabContent = document.getElementById(`${targetTab}-tab`);
      if (targetTabContent) {
        targetTabContent.classList.remove('hidden');
        console.log(`Switched to ${targetTab} tab successfully`);
      } else {
        console.error(`Tab content not found: ${targetTab}-tab`);
      }

      // Load tab-specific data
      if (targetTab === 'classifieds') {
        loadUserClassifieds();
      } else if (targetTab === 'business') {
        loadUserBusinessListings();
      } else if (targetTab === 'support') {
        loadSupportHistory();
      }
    }

    // Check authentication and load profile
    async function checkAuthAndLoadProfile() {
      if (!window.authFunctions) {
        setTimeout(checkAuthAndLoadProfile, 100);
        return;
      }

      currentUser = window.authFunctions.getCurrentUser();

      if (!currentUser) {
        authRequired?.classList.remove('hidden');
        accountContent?.classList.add('hidden');
        return;
      }

      authRequired?.classList.add('hidden');
      accountContent?.classList.remove('hidden');

      await loadUserProfile(currentUser);
      initializeTabs();
      checkAdminAccess(currentUser);
    }

    async function loadUserProfile(user) {
      try {
        // Load user profile data using API endpoint
        const response = await fetch(`/api/user/profile?id=${user.id}`);
        const result = await response.json();

        if (!response.ok) {
          console.error('Error loading profile:', result.error);
          return;
        }

        userProfile = result.profile;

        // Populate form fields
        document.getElementById('display-name').value = userProfile?.display_name || user.user_metadata?.display_name || user.email?.split('@')[0] || '';
        document.getElementById('email').value = user.email || '';
        document.getElementById('bio').value = userProfile?.bio || '';
        document.getElementById('location').value = userProfile?.location || '';

        // Update welcome message
        const welcomeDiv = document.getElementById('user-welcome');
        const displayNameSpan = document.getElementById('user-display-name');
        if (welcomeDiv && displayNameSpan) {
          displayNameSpan.textContent = userProfile?.display_name || user.email?.split('@')[0] || 'User';
          welcomeDiv.classList.remove('hidden');
        }

        // Update bio counter
        updateBioCounter();

      } catch (error) {
        console.error('Error loading profile:', error);
      }
    }

    // Load user's classified posts
    async function loadUserClassifieds() {
      if (!currentUser) return;

      try {
        const response = await fetch(`/api/user/classifieds?userId=${currentUser.id}`);
        const result = await response.json();

        if (response.ok) {
          displayUserClassifieds(result.posts || []);
          updateClassifiedStats(result.stats || {});
        } else {
          console.error('Error loading classifieds:', result.error);
        }
      } catch (error) {
        console.error('Error loading classifieds:', error);
      }
    }

    // Display user's classified posts
    function displayUserClassifieds(posts) {
      const container = document.getElementById('user-posts-list');
      if (!container) return;

      if (posts.length === 0) {
        container.innerHTML = `
          <div class="text-center py-8 text-slate-500">
            <span class="text-4xl mb-4 block">📝</span>
            <p>No classified posts yet</p>
            <p class="text-sm mt-2">Start by posting your first classified ad</p>
          </div>
        `;
        return;
      }

      container.innerHTML = posts.map(post => `
        <div class="bg-white border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow ${!post.is_active ? 'opacity-75' : ''}">
          <div class="flex items-center justify-between">
            <div class="flex-1 ${post.is_active ? 'cursor-pointer' : 'cursor-not-allowed'}" ${post.is_active ? `onclick="viewClassified('${post.id}', '${post.city_slug}', '${post.category}')"` : ''}>
              <h3 class="font-semibold ${post.is_active ? 'text-slate-900 hover:text-emerald-600' : 'text-slate-500 line-through'} transition-colors">
                ${post.title}
                ${!post.is_active ? '<span class="ml-2 text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">Hidden from Public</span>' : ''}
              </h3>
              <p class="text-sm ${post.is_active ? 'text-slate-600' : 'text-slate-400'} mt-1 ${!post.is_active ? 'line-through' : ''}">${post.category} • ${post.city_name || post.city_slug}</p>
              <div class="flex items-center space-x-4 mt-2 text-xs text-slate-500">
                <span>👁️ ${post.view_count || 0} views</span>
                <span>📅 ${new Date(post.created_at).toLocaleDateString()}</span>
                <span class="px-2 py-1 rounded-full text-xs ${post.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                  ${post.is_active ? '✅ Active' : '👁️‍🗨️ Hidden'}
                </span>
                ${!post.is_active ? '<span class="text-orange-600 text-xs">⚠️ Click "Show" to make visible to public</span>' : ''}
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <button onclick="editClassified('${post.id}', '${post.city_slug}', '${post.category}')" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                Edit
              </button>
              <button onclick="toggleClassifiedVisibility('${post.id}', ${post.is_active})" class="${post.is_active ? 'text-yellow-600 hover:text-yellow-700' : 'text-green-600 hover:text-green-700'} text-sm font-medium">
                ${post.is_active ? 'Hide' : 'Show'}
              </button>
              <button onclick="deleteClassified('${post.id}')" class="text-red-600 hover:text-red-700 text-sm font-medium">
                Delete
              </button>
            </div>
          </div>
        </div>
      `).join('');
    }

    // Update classified stats
    function updateClassifiedStats(stats) {
      document.getElementById('total-posts').textContent = stats.total || 0;
      document.getElementById('active-posts').textContent = stats.active || 0;
      document.getElementById('hidden-posts').textContent = stats.hidden || 0;
      document.getElementById('total-views').textContent = stats.totalViews || 0;
    }

    // Load user's business listings
    async function loadUserBusinessListings() {
      if (!currentUser) return;

      try {
        const response = await fetch(`/api/user/business-listings?userId=${currentUser.id}`);
        const result = await response.json();

        if (response.ok) {
          displayUserBusinessListings(result.listings || []);
        } else {
          console.error('Error loading business listings:', result.error);
        }
      } catch (error) {
        console.error('Error loading business listings:', error);
      }
    }

    // Display user's business listings
    function displayUserBusinessListings(listings) {
      const container = document.getElementById('user-business-list');
      if (!container) return;

      if (listings.length === 0) {
        container.innerHTML = `
          <div class="text-center py-8 text-slate-500">
            <span class="text-4xl mb-4 block">🏢</span>
            <p>No business listings found</p>
            <p class="text-sm mt-2">
              <a href="/list-business" class="text-blue-600 hover:text-blue-700 font-medium">Submit your business</a>
              to get started
            </p>
          </div>
        `;
        return;
      }

      container.innerHTML = listings.map(listing => {
        const statusInfo = getBusinessStatusInfo(listing.listing_status);
        const displayName = listing.display_name || listing.business_name;
        const isSubmitted = listing.owner_user_id === currentUser?.id;
        const isClaimed = listing.claimed_by_user_id === currentUser?.id;

        return `
          <div class="border border-slate-200 rounded-xl p-6 hover:shadow-md transition-all duration-200 bg-white">
            <!-- Header with Status -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h4 class="font-semibold text-slate-900 text-lg">${displayName}</h4>
                  <span class="px-3 py-1 text-xs font-medium rounded-full ${statusInfo.bgColor} ${statusInfo.textColor}">
                    ${statusInfo.icon} ${statusInfo.label}
                  </span>
                </div>
                <p class="text-slate-600 mb-1">${listing.city_name} • ${listing.category_name}</p>
                <p class="text-xs text-slate-500">
                  ${isSubmitted ? 'Submitted' : 'Claimed'} ${new Date(listing.created_at).toLocaleDateString()}
                </p>
              </div>

              ${listing.main_image_path ? `
                <div class="w-16 h-16 rounded-lg overflow-hidden bg-slate-100 flex-shrink-0">
                  <img src="${listing.main_image_path}" alt="${displayName}" class="w-full h-full object-cover" />
                </div>
              ` : ''}
            </div>

            <!-- Description -->
            ${listing.description_short ? `
              <p class="text-sm text-slate-600 mb-4 line-clamp-2">${listing.description_short}</p>
            ` : ''}

            <!-- Status-specific content -->
            ${listing.listing_status === 'rejected' && listing.rejection_reason ? `
              <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div class="flex items-start space-x-3">
                  <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span class="text-red-600 text-sm">!</span>
                  </div>
                  <div class="flex-1">
                    <h5 class="font-medium text-red-900 mb-1">Rejection Reason</h5>
                    <p class="text-sm text-red-800">${listing.rejection_reason}</p>
                    <button
                      onclick="showAppealModal('${listing.id}', '${displayName.replace(/'/g, "\\'")}', '${listing.rejection_reason.replace(/'/g, "\\'")}')"
                      class="mt-3 inline-flex items-center space-x-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-lg transition-colors"
                    >
                      <span>📧</span>
                      <span>Contact Admin</span>
                    </button>
                  </div>
                </div>
              </div>
            ` : ''}

            ${listing.listing_status === 'pending_approval' ? `
              <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                    <span class="text-yellow-600 text-sm">⏳</span>
                  </div>
                  <div>
                    <h5 class="font-medium text-yellow-900">Under Review</h5>
                    <p class="text-sm text-yellow-800">We're reviewing your submission. You'll be notified once it's approved.</p>
                  </div>
                </div>
              </div>
            ` : ''}

            ${listing.listing_status === 'active' ? `
              <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div class="flex items-center space-x-3">
                  <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                    <span class="text-green-600 text-sm">✓</span>
                  </div>
                  <div>
                    <h5 class="font-medium text-green-900">Live & Active</h5>
                    <p class="text-sm text-green-800">Your business is live on ExpatsList and visible to users.</p>
                  </div>
                </div>
              </div>
            ` : ''}

            <!-- Actions -->
            <div class="flex items-center justify-between pt-4 border-t border-slate-100">
              <div class="flex space-x-3">
                ${listing.listing_status === 'active' ? `
                  <button onclick="viewBusinessListing('${listing.city_slug}', '${listing.category_slug}', '${listing.slug}')"
                          class="inline-flex items-center space-x-2 px-3 py-2 text-blue-600 hover:text-blue-700 text-sm font-medium">
                    <span>👁️</span>
                    <span>View Live</span>
                  </button>
                ` : ''}

                ${(isClaimed || isSubmitted) && listing.listing_status === 'active' ? `
                  <button onclick="editBusiness('${listing.id}')"
                          class="inline-flex items-center space-x-2 px-3 py-2 text-blue-600 hover:text-blue-700 text-sm font-medium">
                    <span>✏️</span>
                    <span>Edit Business</span>
                  </button>
                ` : ''}
              </div>

              <div class="flex items-center space-x-2 text-xs text-slate-500">
                ${listing.is_verified_expatslist ? '<span class="text-green-600">✅ Verified</span>' : ''}
                ${(isClaimed || isSubmitted) ? '<span class="text-blue-600">🏢 Claimed</span>' : ''}
              </div>
            </div>
          </div>
        `;
      }).join('');
    }

    // Load user's support history
    async function loadSupportHistory() {
      if (!currentUser) return;

      try {
        const response = await fetch(`/api/user/support-history?userId=${currentUser.id}`);
        const result = await response.json();

        if (response.ok) {
          displaySupportHistory(result.requests || []);
        } else {
          console.error('Error loading support history:', result.error);
        }
      } catch (error) {
        console.error('Error loading support history:', error);
      }
    }

    // Display user's support history
    function displaySupportHistory(requests) {
      const container = document.getElementById('support-history-list');
      if (!container) return;

      if (requests.length === 0) {
        container.innerHTML = `
          <div class="text-center py-8 text-slate-500">
            <span class="text-4xl mb-4 block">📧</span>
            <p>No support requests yet</p>
            <p class="text-sm mt-2">Submit your first request below</p>
          </div>
        `;
        return;
      }

      container.innerHTML = requests.map(request => `
        <div class="border border-slate-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
          <div class="flex items-start justify-between mb-3">
            <div class="flex-1">
              <h4 class="font-semibold text-slate-900">${request.subject}</h4>
              <div class="flex items-center space-x-3 mt-1">
                <span class="text-xs px-2 py-1 rounded-full ${getStatusColor(request.status)}">
                  ${getStatusIcon(request.status)} ${request.status.replace('_', ' ').toUpperCase()}
                </span>
                <span class="text-xs text-slate-500">${request.type.replace('_', ' ')}</span>
                <span class="text-xs text-slate-500">${new Date(request.created_at).toLocaleDateString()}</span>
              </div>
            </div>
            <button onclick="toggleConversation('${request.id}')" class="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View Conversation
            </button>
          </div>

          <!-- Conversation Thread -->
          <div id="conversation-${request.id}" class="hidden mt-4 space-y-3 border-t border-slate-100 pt-4">
            <!-- Original Message -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div class="flex items-center space-x-2 mb-2">
                <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                  <span class="text-blue-600 text-xs">👤</span>
                </div>
                <span class="text-sm font-medium text-blue-900">You</span>
                <span class="text-xs text-blue-600">${new Date(request.created_at).toLocaleString()}</span>
              </div>
              <p class="text-sm text-blue-800">${request.message}</p>
            </div>

            <!-- Admin Response (if any) -->
            ${request.admin_response ? `
              <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-3">
                <div class="flex items-center space-x-2 mb-2">
                  <div class="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center">
                    <span class="text-emerald-600 text-xs">🛠️</span>
                  </div>
                  <span class="text-sm font-medium text-emerald-900">ExpatsList Support</span>
                  <span class="text-xs text-emerald-600">${request.updated_at ? new Date(request.updated_at).toLocaleString() : ''}</span>
                </div>
                <p class="text-sm text-emerald-800">${request.admin_response}</p>
              </div>
            ` : `
              <div class="bg-gray-50 border border-gray-200 rounded-lg p-3 text-center">
                <p class="text-sm text-gray-600">⏳ Waiting for admin response...</p>
              </div>
            `}
          </div>
        </div>
      `).join('');
    }

    // Helper functions for support status
    function getStatusColor(status) {
      switch(status) {
        case 'open': return 'bg-red-100 text-red-800';
        case 'in_progress': return 'bg-yellow-100 text-yellow-800';
        case 'resolved': return 'bg-green-100 text-green-800';
        case 'closed': return 'bg-gray-100 text-gray-800';
        default: return 'bg-gray-100 text-gray-800';
      }
    }

    function getStatusIcon(status) {
      switch(status) {
        case 'open': return '🔴';
        case 'in_progress': return '🟡';
        case 'resolved': return '✅';
        case 'closed': return '⚫';
        default: return '❓';
      }
    }

    // Toggle conversation visibility
    window.toggleConversation = function(requestId) {
      const conversation = document.getElementById(`conversation-${requestId}`);
      if (conversation) {
        conversation.classList.toggle('hidden');
      }
    };

    function updateBioCounter() {
      const bioField = document.getElementById('bio');
      const bioCount = document.getElementById('bio-count');
      if (bioField && bioCount) {
        bioCount.textContent = bioField.value.length;
      }
    }

    function showSuccess(message = 'Success!', targetSection = null) {
      clearAccountFeedback();

      // Find the target section or use the first section
      const section = targetSection || document.querySelector('.bg-white.rounded-xl.shadow-sm.border');
      if (!section) {
        // Fallback to existing success message if no section found
        const successText = document.getElementById('success-text');
        if (successText) successText.textContent = message;
        successMessage?.classList.remove('hidden');
        setTimeout(() => successMessage?.classList.add('hidden'), 3000);
        return;
      }

      const successDiv = document.createElement('div');
      successDiv.id = 'account-feedback';
      successDiv.className = 'mb-4 bg-green-50 border border-green-200 rounded-lg p-4 animate-slide-down';
      successDiv.innerHTML = `
        <div class="flex items-start space-x-3">
          <span class="text-green-500 text-lg flex-shrink-0">✅</span>
          <div class="flex-1">
            <p class="font-medium text-green-900 mb-1">Success</p>
            <p class="text-green-800 text-sm">${message}</p>
          </div>
        </div>
      `;

      section.insertBefore(successDiv, section.firstChild);
      successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Auto-hide after 5 seconds
      setTimeout(clearAccountFeedback, 5000);
    }

    function showError(message = 'Error occurred', targetSection = null) {
      clearAccountFeedback();

      // Find the target section or use the first section
      const section = targetSection || document.querySelector('.bg-white.rounded-xl.shadow-sm.border');
      if (!section) {
        // Fallback to existing error message if no section found
        const errorText = document.getElementById('error-text');
        if (errorText) errorText.textContent = message;
        errorMessage?.classList.remove('hidden');
        setTimeout(() => errorMessage?.classList.add('hidden'), 3000);
        return;
      }

      const errorDiv = document.createElement('div');
      errorDiv.id = 'account-feedback';
      errorDiv.className = 'mb-4 bg-red-50 border border-red-200 rounded-lg p-4 animate-slide-down';
      errorDiv.innerHTML = `
        <div class="flex items-start space-x-3">
          <span class="text-red-500 text-lg flex-shrink-0">⚠️</span>
          <div class="flex-1">
            <p class="font-medium text-red-900 mb-1">Error</p>
            <p class="text-red-800 text-sm">${message}</p>
            <button onclick="clearAccountFeedback()" class="mt-2 text-red-600 hover:text-red-700 text-sm underline">
              Dismiss
            </button>
          </div>
        </div>
      `;

      section.insertBefore(errorDiv, section.firstChild);
      errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

      // Auto-hide after 8 seconds
      setTimeout(clearAccountFeedback, 8000);
    }

    function clearAccountFeedback() {
      const feedbackDiv = document.getElementById('account-feedback');
      if (feedbackDiv) {
        feedbackDiv.style.opacity = '0';
        feedbackDiv.style.transform = 'translateY(-10px)';
        setTimeout(() => feedbackDiv.remove(), 300);
      }
    }

    // Global action functions
    window.editClassified = function(postId, citySlug, category) {
      // Redirect to the classified edit page with proper city and category
      window.location.href = `/${citySlug}/classifieds/edit/${postId}`;
    };

    window.viewClassified = function(postId, citySlug, category) {
      // Redirect to the classified detail page
      window.location.href = `/${citySlug}/classifieds/post/${postId}`;
    };

    window.toggleClassifiedVisibility = async function(postId, isActive) {
      try {
        const response = await fetch('/api/user/toggle-classified', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ postId, isActive: !isActive })
        });

        if (response.ok) {
          showSuccess(isActive ? 'Post hidden' : 'Post made visible');
          loadUserClassifieds(); // Reload the list
        } else {
          showError('Failed to update post visibility');
        }
      } catch (error) {
        showError('Error updating post');
      }
    };

    window.deleteClassified = async function(postId) {
      if (!confirm('Are you sure you want to delete this classified post? This action cannot be undone.')) {
        return;
      }

      try {
        const response = await fetch('/api/user/delete-classified', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ postId })
        });

        if (response.ok) {
          showSuccess('Post deleted successfully');
          loadUserClassifieds(); // Reload the list
        } else {
          showError('Failed to delete post');
        }
      } catch (error) {
        showError('Error deleting post');
      }
    };

    window.editBusiness = function(listingId) {
      window.location.href = `/account/business/edit/${listingId}`;
    };

    window.viewBusiness = function(listingId) {
      window.location.href = `/business/${listingId}`;
    };

    window.viewBusinessListing = function(citySlug, categorySlug, businessSlug) {
      window.location.href = `/${citySlug}/${categorySlug}/${businessSlug}`;
    };

    // Helper function to get business status information
    function getBusinessStatusInfo(status) {
      switch(status) {
        case 'active':
          return {
            label: 'Active',
            icon: '✅',
            bgColor: 'bg-green-100',
            textColor: 'text-green-800'
          };
        case 'pending_approval':
          return {
            label: 'Under Review',
            icon: '⏳',
            bgColor: 'bg-yellow-100',
            textColor: 'text-yellow-800'
          };
        case 'rejected':
          return {
            label: 'Rejected',
            icon: '❌',
            bgColor: 'bg-red-100',
            textColor: 'text-red-800'
          };
        case 'inactive':
          return {
            label: 'Inactive',
            icon: '⏸️',
            bgColor: 'bg-gray-100',
            textColor: 'text-gray-800'
          };
        default:
          return {
            label: 'Unknown',
            icon: '❓',
            bgColor: 'bg-gray-100',
            textColor: 'text-gray-800'
          };
      }
    }

    // Show appeal modal for rejected business listings
    window.showAppealModal = function(listingId, businessName, rejectionReason) {
      const modal = document.getElementById('appeal-modal');
      const businessNameSpan = document.getElementById('appeal-business-name');
      const rejectionReasonDiv = document.getElementById('appeal-rejection-reason');
      const listingIdInput = document.getElementById('appeal-listing-id');

      if (modal && businessNameSpan && rejectionReasonDiv && listingIdInput) {
        businessNameSpan.textContent = businessName;
        rejectionReasonDiv.textContent = rejectionReason;
        listingIdInput.value = listingId;
        modal.classList.remove('hidden');

        // Focus on the message textarea
        const messageTextarea = document.getElementById('appeal-message');
        if (messageTextarea) {
          setTimeout(() => messageTextarea.focus(), 100);
        }
      }
    };

    window.hideAppealModal = function() {
      const modal = document.getElementById('appeal-modal');
      if (modal) {
        modal.classList.add('hidden');
        // Reset form
        const form = document.getElementById('appeal-form');
        if (form) form.reset();
      }
    };

    // Event listeners
    document.getElementById('account-signin-btn')?.addEventListener('click', () => {
      window.authFunctions?.showAuthModal();
    });

    document.getElementById('bio')?.addEventListener('input', updateBioCounter);

    // Password strength checker
    function checkPasswordStrength(password) {
      let strength = 0;
      let feedback = [];

      if (password.length >= 8) strength += 1;
      else feedback.push('At least 8 characters');

      if (/[a-z]/.test(password)) strength += 1;
      else feedback.push('Lowercase letter');

      if (/[A-Z]/.test(password)) strength += 1;
      else feedback.push('Uppercase letter');

      if (/[0-9]/.test(password)) strength += 1;
      else feedback.push('Number');

      if (/[^A-Za-z0-9]/.test(password)) strength += 1;
      else feedback.push('Special character');

      return { strength, feedback };
    }

    // Password input handlers
    document.getElementById('new-password')?.addEventListener('input', (e) => {
      const password = e.target.value;
      const strengthDiv = document.getElementById('password-strength');
      const strengthBar = document.getElementById('strength-bar');
      const strengthText = document.getElementById('strength-text');

      if (password.length > 0) {
        strengthDiv.classList.remove('hidden');
        const { strength } = checkPasswordStrength(password);

        const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-blue-500', 'bg-green-500'];
        const texts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        const widths = ['20%', '40%', '60%', '80%', '100%'];

        strengthBar.className = `h-2 rounded-full transition-all duration-300 ${colors[strength - 1] || 'bg-gray-300'}`;
        strengthBar.style.width = widths[strength - 1] || '0%';
        strengthText.textContent = texts[strength - 1] || 'Too Short';
        strengthText.className = `text-xs font-medium ${strength >= 3 ? 'text-green-600' : strength >= 2 ? 'text-yellow-600' : 'text-red-600'}`;
      } else {
        strengthDiv.classList.add('hidden');
      }
    });

    document.getElementById('confirm-password')?.addEventListener('input', (e) => {
      const confirmPassword = e.target.value;
      const newPassword = document.getElementById('new-password').value;
      const matchDiv = document.getElementById('password-match');
      const matchText = document.getElementById('match-text');

      if (confirmPassword.length > 0) {
        matchDiv.classList.remove('hidden');
        if (confirmPassword === newPassword) {
          matchText.textContent = '✓ Passwords match';
          matchText.className = 'text-green-600';
        } else {
          matchText.textContent = '✗ Passwords do not match';
          matchText.className = 'text-red-600';
        }
      } else {
        matchDiv.classList.add('hidden');
      }
    });

    // Password change form handler
    document.getElementById('password-form')?.addEventListener('submit', async (e) => {
      e.preventDefault();

      if (!window.authFunctions) {
        showError('Authentication system not loaded');
        return;
      }

      const newPassword = document.getElementById('new-password').value;
      const confirmPassword = document.getElementById('confirm-password').value;

      if (newPassword !== confirmPassword) {
        showError('New passwords do not match');
        return;
      }

      const { strength } = checkPasswordStrength(newPassword);
      if (strength < 2) {
        showError('Password is too weak. Please choose a stronger password.');
        return;
      }

      const currentPassword = document.getElementById('current-password').value;

      if (!currentPassword) {
        showError('Current password is required');
        return;
      }

      const changeBtn = document.getElementById('change-password-btn');
      const changeText = document.getElementById('change-password-text');
      const changeSpinner = document.getElementById('change-password-spinner');

      changeBtn.disabled = true;
      changeText.textContent = 'Updating...';
      changeSpinner.classList.remove('hidden');

      try {
        // Use the API endpoint for password change
        const response = await fetch('/api/user/change-password', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            currentPassword: currentPassword,
            newPassword: newPassword,
            userId: currentUser.id
          })
        });

        const result = await response.json();

        if (!response.ok) {
          showError(result.error || 'Failed to update password');
        } else {
          showSuccess('Password updated successfully');
          document.getElementById('password-form').reset();
          document.getElementById('password-strength').classList.add('hidden');
          document.getElementById('password-match').classList.add('hidden');
        }
      } catch (error) {
        console.error('Error updating password:', error);
        showError('Error updating password');
      } finally {
        changeBtn.disabled = false;
        changeText.textContent = 'Update Password';
        changeSpinner.classList.add('hidden');
      }
    });

    // Support form handler
    document.getElementById('support-form')?.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(e.target);
      const supportData = {
        userId: currentUser?.id,
        userEmail: currentUser?.email,
        type: formData.get('support_type'),
        subject: formData.get('subject'),
        message: formData.get('message')
      };

      const submitBtn = document.getElementById('submit-support-btn');
      const submitText = document.getElementById('submit-support-text');
      const submitSpinner = document.getElementById('submit-support-spinner');

      submitBtn.disabled = true;
      submitText.textContent = 'Sending...';
      submitSpinner?.classList.remove('hidden');

      try {
        const response = await fetch('/api/support/submit', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(supportData)
        });

        if (response.ok) {
          showSuccess('Support request submitted successfully');
          document.getElementById('support-form').reset();
        } else {
          showError('Failed to submit support request');
        }
      } catch (error) {
        showError('Error submitting support request');
      } finally {
        submitBtn.disabled = false;
        submitText.textContent = 'Send Message';
        submitSpinner?.classList.add('hidden');
      }
    });

    // Appeal form handler
    document.getElementById('appeal-form')?.addEventListener('submit', async (e) => {
      e.preventDefault();

      if (!currentUser) {
        showError('Please sign in to submit an appeal');
        return;
      }

      const formData = new FormData(e.target);
      const listingId = document.getElementById('appeal-listing-id').value;

      const appealData = {
        userId: currentUser.id,
        userEmail: currentUser.email,
        type: 'business_rejection_appeal',
        subject: formData.get('subject'),
        message: formData.get('message'),
        relatedListingId: listingId
      };

      const submitBtn = document.getElementById('submit-appeal-btn');
      const submitText = document.getElementById('submit-appeal-text');
      const submitSpinner = document.getElementById('submit-appeal-spinner');

      submitBtn.disabled = true;
      submitText.textContent = 'Sending...';
      submitSpinner?.classList.remove('hidden');

      try {
        const response = await fetch('/api/user/submit-support-ticket', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(appealData)
        });

        const result = await response.json();

        if (response.ok) {
          showSuccess('Appeal submitted successfully! We\'ll review your request and get back to you soon.');
          hideAppealModal();
          // Refresh support history if on support tab
          loadSupportHistory();
        } else {
          showError(result.error || 'Failed to submit appeal');
        }
      } catch (error) {
        showError('Error submitting appeal');
      } finally {
        submitBtn.disabled = false;
        submitText.textContent = 'Send Appeal';
        submitSpinner?.classList.add('hidden');
      }
    });

    // Delete account handler
    document.getElementById('delete-account-btn')?.addEventListener('click', async () => {
      if (!currentUser) return;

      // Show inline confirmation instead of popup
      showDeleteAccountConfirmation();
    });

    function showDeleteAccountConfirmation() {
      clearAccountFeedback();
      const dangerSection = document.querySelector('#danger-zone');
      if (!dangerSection) return;

      const confirmDiv = document.createElement('div');
      confirmDiv.id = 'account-feedback';
      confirmDiv.className = 'mb-4 bg-red-50 border border-red-200 rounded-lg p-6 animate-slide-down';
      confirmDiv.innerHTML = `
        <div class="text-center">
          <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <span class="text-2xl">⚠️</span>
          </div>
          <h4 class="font-semibold text-red-900 mb-2">Delete Account Permanently</h4>
          <p class="text-red-800 text-sm mb-4">
            This action cannot be undone and will delete all your posts and data permanently.
          </p>
          <div class="flex space-x-3 justify-center">
            <button onclick="proceedWithAccountDeletion()" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg font-medium transition-colors">
              Yes, Delete My Account
            </button>
            <button onclick="clearAccountFeedback()" class="bg-slate-200 hover:bg-slate-300 text-slate-700 px-6 py-2 rounded-lg font-medium transition-colors">
              Cancel
            </button>
          </div>
        </div>
      `;

      dangerSection.insertBefore(confirmDiv, dangerSection.firstChild);
      confirmDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    async function proceedWithAccountDeletion() {
      if (!currentUser) return;

      try {
        const response = await fetch('/api/user/delete-account', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ userId: currentUser.id })
        });

        if (response.ok) {
          await window.authFunctions.deleteUser();
          // Show success and redirect
          clearAccountFeedback();
          showSuccess('Account deleted successfully. Redirecting...', document.querySelector('#danger-zone'));
          setTimeout(() => {
            window.location.href = '/';
          }, 2000);
        } else {
          const result = await response.json();
          showError('Error deleting account: ' + (result.error || 'Unknown error'), document.querySelector('#danger-zone'));
        }
      } catch (error) {
        console.error('Error deleting account:', error);
        showError('Error deleting account', document.querySelector('#danger-zone'));
      }
    }

    // Make function globally available
    window.proceedWithAccountDeletion = proceedWithAccountDeletion;

    // Profile form handler
    profileForm?.addEventListener('submit', async (e) => {
      e.preventDefault();

      if (!currentUser) return;

      const saveBtn = document.getElementById('save-profile-btn');
      const saveText = document.getElementById('save-profile-text');
      const saveSpinner = document.getElementById('save-profile-spinner');

      saveBtn.disabled = true;
      saveText.textContent = 'Saving...';
      saveSpinner?.classList.remove('hidden');

      try {
        const formData = new FormData(e.target);
        const profileData = {
          id: currentUser.id,
          email: currentUser.email,
          display_name: formData.get('display_name'),
          bio: formData.get('bio'),
          location: formData.get('location')
        };

        const response = await fetch('/api/user/update-profile', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(profileData)
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Failed to update profile');
        }

        showSuccess('Profile updated successfully');

        // Update welcome message
        const displayNameSpan = document.getElementById('user-display-name');
        if (displayNameSpan) {
          displayNameSpan.textContent = profileData.display_name || currentUser.email?.split('@')[0] || 'User';
        }

        document.dispatchEvent(new CustomEvent('authStateChanged'));

      } catch (error) {
        console.error('Error updating profile:', error);
        showError('Failed to update profile. Please try again.');
      } finally {
        saveBtn.disabled = false;
        saveText.textContent = 'Save Changes';
        saveSpinner?.classList.add('hidden');
      }
    });

    // Business action handlers
    document.getElementById('claim-business-btn')?.addEventListener('click', () => {
      // Open business search modal or redirect to claim page
      window.location.href = '/account/business/claim';
    });

    document.getElementById('verify-business-btn')?.addEventListener('click', () => {
      // Open verification process
      window.location.href = '/account/business/verify';
    });

    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkAuthAndLoadProfile);

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', checkAuthAndLoadProfile);

    // Also try immediately in case DOM is already ready
    checkAuthAndLoadProfile();

    // Check for tab parameter in URL and switch to that tab
    function checkTabParameter() {
      const urlParams = new URLSearchParams(window.location.search);
      const tabParam = urlParams.get('tab');

      if (tabParam) {
        console.log('Tab parameter found:', tabParam);

        // Wait for content to be loaded before switching tabs
        const checkContentLoaded = setInterval(() => {
          const accountContent = document.getElementById('account-content');
          if (accountContent && !accountContent.classList.contains('hidden')) {
            clearInterval(checkContentLoaded);

            // Switch to the specified tab
            if (tabParam === 'classifieds') {
              switchToTab('classifieds');

              // Show a subtle success message
              setTimeout(() => {
                showSuccess('Your classified ad has been updated successfully!');
              }, 500);
            } else if (tabParam === 'business') {
              switchToTab('business');
            } else if (tabParam === 'security') {
              switchToTab('security');
            } else if (tabParam === 'support') {
              switchToTab('support');
            }

            // Clean up URL parameter
            const newUrl = window.location.pathname;
            window.history.replaceState({}, document.title, newUrl);
          }
        }, 100);

        // Timeout after 5 seconds
        setTimeout(() => {
          clearInterval(checkContentLoaded);
        }, 5000);
      }
    }

    // Call tab parameter check after a short delay
    setTimeout(checkTabParameter, 100);

    // Check if user has admin access and show admin panel link
    async function checkAdminAccess(user) {
      if (!user || !user.email) {
        return;
      }

      console.log('Checking admin access for user:', user.email);

      // Check if user is main admin
      if (user.email === '<EMAIL>') {
        showAdminPanelLink();
        console.log('Admin access granted to main admin:', user.email);
        return;
      }

      // Check if user has administrator role in database
      try {
        const response = await fetch('/api/user/profile', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        });

        if (response.ok) {
          const profileData = await response.json();
          if (profileData.profile && profileData.profile.role === 'administrator') {
            showAdminPanelLink();
            console.log('Admin access granted to database admin:', user.email);
          }
        }
      } catch (error) {
        console.error('Error checking admin role:', error);
      }
    }

    function showAdminPanelLink() {
      const adminLink = document.getElementById('admin-panel-link');
      if (adminLink) {
        adminLink.classList.remove('hidden');
        adminLink.classList.add('flex');
        console.log('Admin panel link shown');
      }
    }
  </script>

  <style>
    .password-strength-bar {
      transition: all 0.3s ease;
    }

    @keyframes slide-down {
      from {
        opacity: 0;
        transform: translateY(-20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .animate-slide-down {
      animation: slide-down 0.4s ease-out;
    }

    /* Line clamp utility for text truncation */
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    /* Modal backdrop blur effect */
    #appeal-modal {
      backdrop-filter: blur(4px);
    }

    /* Smooth transitions for business listing cards */
    .business-listing-card {
      transition: all 0.2s ease-in-out;
    }

    .business-listing-card:hover {
      transform: translateY(-2px);
    }

    /* Status badge animations */
    .status-badge {
      transition: all 0.2s ease-in-out;
    }

    /* Appeal modal animations */
    #appeal-modal > div {
      animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
      from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
      }
      to {
        opacity: 1;
        transform: scale(1) translateY(0);
      }
    }
  </style>
</Layout>
