import type { APIRoute } from 'astro';
import { query } from '../../../lib/database';

export const GET: APIRoute = async ({ request, url }) => {
  try {
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get user's business listings (both submitted and claimed)
    const result = await query(`
      SELECT
        l.id,
        l.business_name,
        l.display_name,
        l.slug,
        l.category_primary_id,
        l.city_id,
        l.listing_status,
        l.is_verified_expatslist,
        l.claimed_by_user_id,
        l.owner_user_id,
        l.created_at,
        l.updated_at,
        l.main_image_path,
        l.description_short,
        c.name as category_name,
        c.slug as category_slug,
        ci.name as city_name,
        ci.path_slug as city_slug,
        -- Get rejection reason from admin notifications
        (
          SELECT an.data->>'rejectionReason'
          FROM admin_notifications an
          WHERE an.type = 'business_rejection'
          AND an.data->>'listingId' = l.id::text
          ORDER BY an.created_at DESC
          LIMIT 1
        ) as rejection_reason
      FROM listings l
      LEFT JOIN categories c ON l.category_primary_id = c.id
      LEFT JOIN cities ci ON l.city_id = ci.id
      WHERE (l.claimed_by_user_id = $1 OR l.owner_user_id = $1)
      AND l.deleted_at IS NULL
      ORDER BY l.created_at DESC
    `, [userId]);

    return new Response(JSON.stringify({
      listings: result.rows || []
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error in user business listings API:', error);
    return new Response(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
