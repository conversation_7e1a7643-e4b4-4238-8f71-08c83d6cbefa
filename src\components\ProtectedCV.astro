---
export interface Props {
  cvUrl: string;
  filename?: string;
  fileSize?: number;
  className?: string;
}

const { cvUrl, filename, fileSize, className = '' } = Astro.props;
---

<div class={`protected-cv-container ${className}`}>
  <!-- CV Section for Authenticated Users -->
  <div id="cv-authenticated" class="hidden">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 md:p-4 mb-6">
      <div class="flex items-center justify-between mb-3">
        <h3 class="font-semibold text-blue-900 text-sm md:text-base flex items-center">
          <span class="mr-2">📄</span>
          CV / Portfolio
        </h3>
        <a 
          href={cvUrl} 
          target="_blank" 
          rel="noopener noreferrer"
          class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors duration-200"
        >
          Download
        </a>
      </div>
      
      <!-- File Info -->
      <div class="flex items-center space-x-2 mb-3 text-xs text-slate-600">
        <span class="truncate font-medium">
          {filename || 'CV/Portfolio.pdf'}
        </span>
        {fileSize && (
          <span class="text-slate-500">
            • {(fileSize / 1024 / 1024).toFixed(1)} MB
          </span>
        )}
      </div>

      <!-- Embedded PDF Viewer -->
      <div class="bg-white rounded border border-blue-200 overflow-hidden">
        <div class="relative h-48 sm:h-64 md:h-80">
          <iframe
            src={`${cvUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
            class="w-full h-full border-0"
            title="CV/Portfolio Preview"
            loading="lazy"
            onload="handlePDFLoad(this)"
            onerror="handlePDFError(this)"
          ></iframe>
          <!-- Fallback for browsers that don't support PDF embedding -->
          <div class="absolute inset-0 flex items-center justify-center bg-slate-100 hidden" id="pdf-fallback">
            <div class="text-center p-4">
              <div class="text-3xl mb-2">📄</div>
              <p class="text-slate-600 text-sm mb-3">PDF preview not available</p>
              <a 
                href={cvUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded text-xs font-medium transition-colors duration-200"
              >
                Open PDF
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- CV Section for Non-Authenticated Users (Blurred with Sign-in Prompt) -->
  <div id="cv-protected" class="block">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 md:p-4 mb-6 relative overflow-hidden">
      <!-- Blurred Background Content -->
      <div class="filter blur-sm pointer-events-none select-none">
        <div class="flex items-center justify-between mb-3">
          <h3 class="font-semibold text-blue-900 text-sm md:text-base flex items-center">
            <span class="mr-2">📄</span>
            CV / Portfolio
          </h3>
          <div class="bg-blue-600 text-white px-3 py-1.5 rounded text-xs font-medium">
            Download
          </div>
        </div>
        
        <!-- Blurred File Info -->
        <div class="flex items-center space-x-2 mb-3 text-xs text-slate-600">
          <span class="truncate font-medium">
            {filename || 'resume-sample.pdf'}
          </span>
          {fileSize && (
            <span class="text-slate-500">
              • {(fileSize / 1024 / 1024).toFixed(1)} MB
            </span>
          )}
        </div>

        <!-- Blurred PDF Preview -->
        <div class="bg-white rounded border border-blue-200 overflow-hidden">
          <div class="relative h-48 sm:h-64 md:h-80 bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
            <div class="text-center p-8">
              <div class="text-6xl mb-4 opacity-30">📄</div>
              <div class="space-y-2 opacity-40">
                <div class="h-3 bg-slate-300 rounded w-3/4 mx-auto"></div>
                <div class="h-3 bg-slate-300 rounded w-1/2 mx-auto"></div>
                <div class="h-3 bg-slate-300 rounded w-2/3 mx-auto"></div>
                <div class="h-3 bg-slate-300 rounded w-1/3 mx-auto"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sign-in Overlay -->
      <div class="absolute inset-0 bg-white/95 backdrop-blur-sm flex items-center justify-center">
        <div class="text-center p-6 max-w-sm mx-auto">
          <!-- Lock Icon with Animation -->
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
            <span class="text-white text-2xl animate-pulse">🔒</span>
          </div>
          
          <!-- Heading -->
          <h4 class="text-lg font-bold text-slate-900 mb-2">
            CV/Portfolio Protected
          </h4>
          
          <!-- Description -->
          <p class="text-sm text-slate-600 mb-4 leading-relaxed">
            Sign in to view and download this candidate's CV and portfolio documents.
          </p>
          
          <!-- Sign-in Button -->
          <button
            onclick="showSignInModal()"
            class="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
          >
            <span class="flex items-center justify-center">
              <span class="mr-2">🔑</span>
              Sign In to View CV
            </span>
          </button>
          
          <!-- Additional Info -->
          <p class="text-xs text-slate-500 mt-3">
            Free account • No spam • Secure access
          </p>
        </div>
      </div>
    </div>
  </div>
</div>

<script is:inline>
  // Initialize CV protection based on auth state
  document.addEventListener('DOMContentLoaded', function() {
    checkCVAccess();
    
    // Listen for auth state changes
    document.addEventListener('authStateChanged', checkCVAccess);
  });

  function checkCVAccess() {
    const currentUser = window.authFunctions?.getCurrentUser();
    const authenticatedSection = document.getElementById('cv-authenticated');
    const protectedSection = document.getElementById('cv-protected');
    
    if (currentUser && authenticatedSection && protectedSection) {
      // User is signed in - show real CV
      authenticatedSection.classList.remove('hidden');
      protectedSection.classList.add('hidden');
    } else if (authenticatedSection && protectedSection) {
      // User is not signed in - show protected version
      authenticatedSection.classList.add('hidden');
      protectedSection.classList.remove('hidden');
    }
  }

  // PDF loading handlers
  window.handlePDFLoad = function(iframe) {
    // PDF loaded successfully
    const fallback = document.getElementById('pdf-fallback');
    if (fallback) {
      fallback.classList.add('hidden');
    }
  };

  window.handlePDFError = function(iframe) {
    // PDF failed to load, show fallback
    iframe.style.display = 'none';
    const fallback = document.getElementById('pdf-fallback');
    if (fallback) {
      fallback.classList.remove('hidden');
    }
  };

  // Show sign-in modal (this function should be available globally)
  window.showSignInModal = function() {
    if (window.authFunctions && window.authFunctions.showSignInModal) {
      window.authFunctions.showSignInModal();
    } else {
      // Fallback - redirect to sign-in page
      window.location.href = '/auth/signin?redirect=' + encodeURIComponent(window.location.pathname);
    }
  };
</script>

<style>
  /* Smooth transitions for auth state changes */
  .protected-cv-container > div {
    transition: opacity 0.3s ease-in-out;
  }

  /* Enhanced blur effect for better visual separation */
  .filter.blur-sm {
    filter: blur(4px);
  }

  /* Prevent text selection on blurred content */
  .select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* Skeleton loading animation for blurred content */
  @keyframes skeleton-loading {
    0% {
      opacity: 0.4;
    }
    50% {
      opacity: 0.6;
    }
    100% {
      opacity: 0.4;
    }
  }

  .opacity-40 > div {
    animation: skeleton-loading 2s ease-in-out infinite;
  }

  /* Hover effects for the sign-in button */
  .protected-cv-container button:hover {
    transform: translateY(-2px);
  }
</style>
